{"name": "prompt-as-one-page-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "validate:figma": "tsx src/lib/figma/validator.ts"}, "dependencies": {"@ai-sdk/openai": "^0.0.66", "@tanstack/react-query": "^5.51.0", "ai": "^3.3.0", "next": "14.2.5", "react": "^18", "react-dom": "^18", "uuid": "^11.1.0", "zustand": "^4.5.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "eslint": "^8", "eslint-config-next": "14.2.5", "eslint-plugin-tailwindcss": "^3.17.4", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.16.2", "typescript": "^5"}}