import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
  weight: ['400', '500', '600'],
  style: ['normal'],
})

export const metadata: Metadata = {
  title: 'AI-Powered Code Generation Interface',
  description: 'A Notion-like AI-enhanced editing interface with intelligent code generation',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={inter.variable}>
      <body className={`${inter.className} antialiased bg-neutral-900 text-neutral-50`}>
        {children}
      </body>
    </html>
  )
}