import { Suspense } from 'react';
import { LayoutWindow } from '@/components/figma/Window/LayoutWindow';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface DocPageProps {
  params: { gid: string };
}

/**
 * 动态文档页面 - 基于GID的独立Notion Page
 * 严格按照Figma设计实现单页面应用布局
 */
export default function DocPage({ params }: DocPageProps) {
  return (
    <div className="min-h-screen bg-neutral-900">
      <Suspense fallback={<LoadingSpinner />}>
        <LayoutWindow gid={params.gid} />
      </Suspense>
    </div>
  );
}

/**
 * 生成页面元数据
 */
export async function generateMetadata({ params }: DocPageProps) {
  return {
    title: `Document ${params.gid} - AI Code Generation`,
    description: `AI-powered editing interface for document ${params.gid}`,
  };
}