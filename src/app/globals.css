@tailwind base;
@tailwind components;
@tailwind utilities;

/* 基于Figma设计的自定义CSS变量 */
:root {
  /* Figma颜色token */
  --color-page-bg: #171717;
  --color-sidebar-bg: #262626;
  --color-panel-bg: #0a0a0a;
  --color-text-primary: #fafafa;
  --color-text-secondary: #a3a3a3;
  --color-text-disabled: #404040;
  --color-brand: #0ea5e9;
  --color-button-primary: #082f49;
  --color-border: #404040;
  --color-success: #4ade80;
  
  /* Figma间距token */
  --spacing-sidebar-width: 296px;
  --spacing-content-max-width: 800px;
  --spacing-search-width: 320px;
  
  /* Figma字体token */
  --font-inter: 'Inter', sans-serif;
  --font-menlo: 'Menlo', 'Monaco', 'Consolas', monospace;
}

/* 基础样式重置，保持Figma设计的精确性 */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  background-color: var(--color-page-bg);
  color: var(--color-text-primary);
  font-family: var(--font-inter);
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 确保Figma字体正确加载 */
.font-inter {
  font-family: var(--font-inter);
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

.font-menlo {
  font-family: var(--font-menlo);
}

/* Figma中定义的加载动画 */
@keyframes loading-spin {
  from {
    transform: rotate(90deg);
  }
  to {
    transform: rotate(450deg);
  }
}

.animate-loading-spin {
  animation: loading-spin 1s linear infinite;
}

/* Figma中定义的渐变文字效果 */
.gradient-text {
  background: linear-gradient(to right, #fafafa, #404040);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 确保所有尺寸都按照Figma精确还原 */
.w-sidebar {
  width: var(--spacing-sidebar-width);
}

.max-w-content {
  max-width: var(--spacing-content-max-width);
}

.w-search {
  width: var(--spacing-search-width);
}