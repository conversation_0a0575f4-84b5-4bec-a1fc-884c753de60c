import Link from 'next/link';

/**
 * 首页 - 文档列表和演示入口
 */
export default function HomePage() {
  return (
    <div className="min-h-screen bg-neutral-900 text-neutral-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-4xl font-bold mb-4">
            AI-Powered Code Generation Interface
          </h1>
          <p className="text-neutral-400 mb-8">
            A Notion-like AI-enhanced editing interface with intelligent code generation capabilities.
          </p>
          
          <div className="space-y-4">
            <h2 className="text-2xl font-semibold mb-4">Demo Documents</h2>
            
            <Link 
              href="/docs/demo-1001"
              className="block p-4 border border-neutral-700 rounded-lg hover:border-neutral-600 transition-colors"
            >
              <h3 className="text-lg font-medium mb-2">Demo Document 1001</h3>
              <p className="text-neutral-400 text-sm">
                Interactive demo showcasing the complete 12-page AI interaction flow based on Figma designs.
              </p>
              <span className="inline-block mt-2 text-sky-400 text-sm">
                View Demo →
              </span>
            </Link>

            <Link 
              href="/docs/sample-project"
              className="block p-4 border border-neutral-700 rounded-lg hover:border-neutral-600 transition-colors"
            >
              <h3 className="text-lg font-medium mb-2">Sample Project</h3>
              <p className="text-neutral-400 text-sm">
                Example document with AI code generation for a Todo app with SQLite and Passkey authentication.
              </p>
              <span className="inline-block mt-2 text-sky-400 text-sm">
                View Demo →
              </span>
            </Link>
          </div>

          <div className="mt-8 p-4 border border-neutral-700 rounded-lg bg-neutral-800">
            <h3 className="text-lg font-medium mb-2">Features</h3>
            <ul className="text-neutral-400 text-sm space-y-1">
              <li>• AI-powered code generation with context awareness</li>
              <li>• Multi-turn conversation support</li>
              <li>• Real-time content generation and editing</li>
              <li>• Structured content with syntax highlighting</li>
              <li>• Notion-like editing experience</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}