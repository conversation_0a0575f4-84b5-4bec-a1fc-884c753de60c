import { openai } from '@ai-sdk/openai';
import { streamText } from 'ai';
import { NextRequest } from 'next/server';

/**
 * AI聊天API路由 - 后端请求转发和System Prompt包装
 * 基于文档GID提供上下文相关的AI响应
 */
export async function POST(req: NextRequest) {
  try {
    const { messages, gid, context } = await req.json();

    // 验证请求数据
    if (!messages || !Array.isArray(messages)) {
      return new Response('Invalid messages format', { status: 400 });
    }

    if (!gid) {
      return new Response('Document GID is required', { status: 400 });
    }

    // 构建基于GID的System Prompt
    const systemPrompt = buildSystemPrompt(gid, context);

    // 使用Vercel AI SDK进行流式响应
    const result = await streamText({
      model: openai('gpt-4'),
      messages: [
        { role: 'system', content: systemPrompt },
        ...messages
      ],
      temperature: 0.7,
      maxTokens: 2000,
    });

    return result.toAIStreamResponse();

  } catch (error) {
    console.error('AI Chat API Error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

/**
 * 基于文档GID构建System Prompt
 */
function buildSystemPrompt(gid: string, context?: any): string {
  const basePrompt = `你是一个AI代码生成助手，专门帮助用户创建和优化代码。

你的能力包括：
- 生成高质量的代码
- 解释技术概念
- 提供最佳实践建议
- 协助调试和优化

请始终：
- 提供清晰、可执行的代码
- 包含必要的注释
- 遵循行业标准和最佳实践
- 针对用户的具体需求进行定制`;

  const docContext = `

当前文档信息：
- 文档ID: ${gid}
- 文档类型: AI增强编辑页面
- 上下文: ${context ? JSON.stringify(context, null, 2) : '无特定上下文'}`;

  return basePrompt + docContext;
}

/**
 * 处理GET请求 - 返回API状态
 */
export async function GET() {
  return Response.json({ 
    status: 'AI Chat API is running',
    timestamp: new Date().toISOString()
  });
}