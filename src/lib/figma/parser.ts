import { FigmaPageData, AIInputState, PAGE_STATE_TO_FILE } from './types';

/**
 * Figma JSON数据解析器
 * 严格按照JSON结构解析，不允许任何修改
 */
export class FigmaDataParser {
  private static dataCache = new Map<string, FigmaPageData>();

  /**
   * 根据AI输入状态获取对应的Figma页面数据
   */
  static async getPageData(state: AIInputState): Promise<FigmaPageData> {
    const fileName = PAGE_STATE_TO_FILE[state];
    
    if (this.dataCache.has(fileName)) {
      return this.dataCache.get(fileName)!;
    }

    try {
      // 从assets目录加载JSON文件
      const response = await fetch(`/assets/1001/${fileName}`);
      if (!response.ok) {
        throw new Error(`Failed to load ${fileName}: ${response.statusText}`);
      }
      
      const data: FigmaPageData = await response.json();
      
      // 验证数据结构
      this.validatePageData(data, fileName);
      
      // 缓存数据
      this.dataCache.set(fileName, data);
      
      return data;
    } catch (error) {
      console.error(`Error loading Figma data for ${state}:`, error);
      throw error;
    }
  }

  /**
   * 验证Figma页面数据结构
   */
  private static validatePageData(data: any, fileName: string): void {
    const required = ['component', 'structure', 'styles', 'designTokens'];
    
    for (const field of required) {
      if (!(field in data)) {
        throw new Error(`Missing required field '${field}' in ${fileName}`);
      }
    }

    if (!data.structure.hierarchy || !Array.isArray(data.structure.hierarchy)) {
      throw new Error(`Invalid hierarchy structure in ${fileName}`);
    }
  }

  /**
   * 提取所有设计token
   */
  static async getAllDesignTokens(): Promise<Record<string, string>> {
    const allTokens: Record<string, string> = {};
    
    for (const state of Object.keys(PAGE_STATE_TO_FILE) as AIInputState[]) {
      try {
        const data = await this.getPageData(state);
        Object.assign(allTokens, data.designTokens);
      } catch (error) {
        console.warn(`Failed to load tokens for ${state}:`, error);
      }
    }
    
    return allTokens;
  }

  /**
   * 提取特定组件的className
   */
  static getComponentClassName(data: FigmaPageData, componentName: string): string {
    const findComponent = (nodes: any[], name: string): string | null => {
      for (const node of nodes) {
        if (node.name === name && node.className) {
          return node.className;
        }
        if (node.children) {
          const found = findComponent(node.children, name);
          if (found) return found;
        }
      }
      return null;
    };

    const className = findComponent(data.structure.hierarchy, componentName);
    if (!className) {
      throw new Error(`Component '${componentName}' not found in Figma data`);
    }
    
    return className;
  }

  /**
   * 清空缓存
   */
  static clearCache(): void {
    this.dataCache.clear();
  }
}