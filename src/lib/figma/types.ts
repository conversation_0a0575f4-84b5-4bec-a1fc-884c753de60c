// 基于Figma JSON结构的严格类型定义

export interface FigmaComponent {
  readonly name: string;
  readonly id: string;
  readonly type: string;
  readonly description?: string;
}

export interface FigmaNode {
  readonly name: string;
  readonly id: string;
  readonly type: string;
  readonly className: string;
  readonly children?: FigmaNode[];
  readonly text?: string;
  readonly props?: Record<string, unknown>;
}

export interface FigmaStructure {
  readonly hierarchy: FigmaNode[];
}

export interface FigmaStyles {
  readonly colors: Record<string, string>;
  readonly dimensions: Record<string, string>;
  readonly spacing: Record<string, string>;
  readonly effects: Record<string, string>;
  readonly typography: Record<string, FigmaTypography>;
}

export interface FigmaTypography {
  readonly family: string;
  readonly style: string;
  readonly size: string;
  readonly weight: number;
  readonly lineHeight: string | number;
}

export interface FigmaDesignTokens {
  readonly [key: string]: string;
}

export interface FigmaPageData {
  readonly component: FigmaComponent;
  readonly structure: FigmaStructure;
  readonly styles: FigmaStyles;
  readonly designTokens: FigmaDesignTokens;
  readonly interactions?: Record<string, unknown>;
  readonly states?: Record<string, unknown>;
  readonly animations?: Record<string, unknown>;
}

// AI输入框的12个状态类型
export type AIInputState = 
  | 'before-input'           // page-1
  | 'input-on-focus'         // page-2  
  | 'prompt-entered'         // page-3
  | 'submitting-state'       // page-4
  | 'generating'             // page-5
  | 'generating-with-ai-question' // page-6
  | 'thread-continuation'    // page-7
  | 'thread-input-filled'    // page-8
  | 'prompt-sending-process' // page-9
  | 'conversation-collapsed' // page-10
  | 'ai-generating-structured-content' // page-11
  | 'generation-completed'   // page-12

// 页面状态到文件名的映射
export const PAGE_STATE_TO_FILE: Record<AIInputState, string> = {
  'before-input': 'page-1-before-input.json',
  'input-on-focus': 'page-2-input-on-focus.json', 
  'prompt-entered': 'page-3-prompt-entered.json',
  'submitting-state': 'page-4-submitting-state.json',
  'generating': 'page-5-generating.json',
  'generating-with-ai-question': 'page-6-generating-with-ai-question.json',
  'thread-continuation': 'page-7-thread-continuation.json',
  'thread-input-filled': 'page-8-thread-input-filled.json',
  'prompt-sending-process': 'page-9-prompt-sending-process.json',
  'conversation-collapsed': 'page-10-conversation-collapsed.json',
  'ai-generating-structured-content': 'page-11-ai-generating-structured-content.json',
  'generation-completed': 'page-12-generation-completed.json',
} as const;