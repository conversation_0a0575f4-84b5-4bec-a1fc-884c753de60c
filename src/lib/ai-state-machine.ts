// AI助手状态机定义
export type AIState =
  | 'before-input'           // 初始状态：等待用户输入
  | 'input-on-focus'         // 聚焦状态：用户点击输入框
  | 'prompt-entered'         // 输入完成：用户填写了内容
  | 'submitting-state'       // 提交中：正在发送给AI
  | 'generating'             // 生成中：AI正在生成响应
  | 'generating-with-ai-question' // AI询问：AI提出问题等待用户选择
  | 'thread-continuation'    // Thread模式：用户选择继续对话
  | 'thread-input-filled'    // Thread输入：用户在Thread中输入了内容
  | 'prompt-sending-process' // Thread提交：Thread内容提交中
  | 'conversation-collapsed' // 对话折叠：内容已生成，对话被折叠
  | 'ai-generating-structured-content' // 结构化生成：AI生成结构化内容
  | 'generation-completed'   // 生成完成：最终状态

// 状态转换事件
export type AIEvent =
  | 'FOCUS_INPUT'
  | 'ENTER_TEXT'
  | 'BLUR_INPUT'
  | 'SUBMIT_PROMPT'
  | 'AI_START_GENERATING'
  | 'AI_ASK_QUESTION'
  | 'USER_SELECT_THREAD'
  | 'ENTER_THREAD_TEXT'
  | 'SUBMIT_THREAD'
  | 'COLLAPSE_CONVERSATION'
  | 'START_STRUCTURED_GENERATION'
  | 'COMPLETE_GENERATION'

// 单个块的状态接口
export interface BlockState {
  // 当前AI状态
  currentState: AIState;

  // 用户输入数据
  userInput: string;
  threadInput: string;

  // AI生成的内容
  aiResponse: string;
  aiQuestion?: string;
  structuredContent?: StructuredContent;

  // UI状态
  isInputFocused: boolean;
  isSubmitting: boolean;
  isGenerating: boolean;

  // 对话历史
  conversationHistory: ConversationItem[];
}

// 单个块的完整信息
export interface PromptResponseBlock {
  id: string;
  createdAt: number;
  state: BlockState;
  stateMachine: AIStateMachine;
}

// 多块应用状态
export interface AppState {
  // 所有块的集合
  blocks: PromptResponseBlock[];

  // 当前活跃的块ID
  activeBlockId?: string;

  // 全局UI状态
  isCollapsed: boolean;

  // 是否有任何块在生成中（用于侧边栏显示）
  hasGeneratingBlocks: boolean;
}

export interface ConversationItem {
  type: 'user' | 'ai' | 'question';
  content: string;
  timestamp: number;
}

export interface StructuredContent {
  title: string;
  sections: ContentSection[];
  codeBlocks: CodeBlock[];
}

export interface ContentSection {
  type: 'h1' | 'h2' | 'ul' | 'p';
  content: string;
  emoji?: string;
}

export interface CodeBlock {
  language: string;
  code: string;
  filename?: string;
}

// 状态转换逻辑
export class AIStateMachine {
  private state: AIState = 'before-input';
  private listeners: ((state: AIState) => void)[] = [];

  getCurrentState(): AIState {
    return this.state;
  }

  transition(event: AIEvent): void {
    const newState = this.getNextState(this.state, event);
    if (newState !== this.state) {
      this.state = newState;
      this.notifyListeners();
    }
  }

  subscribe(listener: (state: AIState) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.state));
  }

  private getNextState(currentState: AIState, event: AIEvent): AIState {
    const transitions: Record<AIState, Partial<Record<AIEvent, AIState>>> = {
      'before-input': {
        'FOCUS_INPUT': 'input-on-focus'
      },
      'input-on-focus': {
        'ENTER_TEXT': 'prompt-entered',
        'BLUR_INPUT': 'before-input'
      },
      'prompt-entered': {
        'SUBMIT_PROMPT': 'submitting-state',
        'BLUR_INPUT': 'before-input'
      },
      'submitting-state': {
        'AI_START_GENERATING': 'generating'
      },
      'generating': {
        'AI_ASK_QUESTION': 'generating-with-ai-question',
        'START_STRUCTURED_GENERATION': 'ai-generating-structured-content'
      },
      'generating-with-ai-question': {
        'USER_SELECT_THREAD': 'thread-continuation'
      },
      'thread-continuation': {
        'ENTER_THREAD_TEXT': 'thread-input-filled'
      },
      'thread-input-filled': {
        'SUBMIT_THREAD': 'prompt-sending-process'
      },
      'prompt-sending-process': {
        'COLLAPSE_CONVERSATION': 'conversation-collapsed'
      },
      'conversation-collapsed': {
        'START_STRUCTURED_GENERATION': 'ai-generating-structured-content'
      },
      'ai-generating-structured-content': {
        'COMPLETE_GENERATION': 'generation-completed'
      },
      'generation-completed': {}
    };

    return transitions[currentState][event] || currentState;
  }
}

// 块管理器 - 管理多个独立的块
export class BlockManager {
  private blocks: Map<string, PromptResponseBlock> = new Map();
  private listeners: ((appState: AppState) => void)[] = [];

  constructor() {
    // 创建初始块
    this.createNewBlock();
  }

  // 创建新块
  createNewBlock(autoFocus: boolean = false): string {
    const id = this.generateBlockId();
    const stateMachine = new AIStateMachine();

    const block: PromptResponseBlock = {
      id,
      createdAt: Date.now(),
      state: {
        currentState: autoFocus ? 'input-on-focus' : 'before-input',
        userInput: '',
        threadInput: '',
        aiResponse: '',
        isInputFocused: autoFocus,
        isSubmitting: false,
        isGenerating: false,
        conversationHistory: []
      },
      stateMachine
    };

    // 如果自动聚焦，转换状态机到输入状态
    if (autoFocus) {
      stateMachine.transition('FOCUS_INPUT');
    }

    // 监听块的状态变化
    stateMachine.subscribe((newState) => {
      this.updateBlockState(id, { currentState: newState });
    });

    this.blocks.set(id, block);
    this.notifyListeners();
    return id;
  }

  // 获取块
  getBlock(id: string): PromptResponseBlock | undefined {
    return this.blocks.get(id);
  }

  // 获取所有块
  getAllBlocks(): PromptResponseBlock[] {
    return Array.from(this.blocks.values()).sort((a, b) => a.createdAt - b.createdAt);
  }

  // 更新块状态
  updateBlockState(id: string, updates: Partial<BlockState>): void {
    const block = this.blocks.get(id);
    if (block) {
      block.state = { ...block.state, ...updates };
      this.notifyListeners();
    }
  }

  // 触发块的状态转换
  transitionBlockState(id: string, event: AIEvent): void {
    const block = this.blocks.get(id);
    if (block) {
      block.stateMachine.transition(event);
    }
  }

  // 删除块
  removeBlock(id: string): void {
    this.blocks.delete(id);
    this.notifyListeners();
  }

  // 获取应用状态
  getAppState(): AppState {
    const blocks = this.getAllBlocks();
    const hasGeneratingBlocks = blocks.some(block =>
      block.state.currentState === 'generating' ||
      block.state.currentState === 'generating-with-ai-question' ||
      block.state.currentState === 'ai-generating-structured-content' ||
      block.state.isGenerating
    );

    return {
      blocks,
      hasGeneratingBlocks,
      isCollapsed: false // 这个可以根据需要调整
    };
  }

  // 订阅状态变化
  subscribe(listener: (appState: AppState) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(): void {
    const appState = this.getAppState();
    this.listeners.forEach(listener => listener(appState));
  }

  private generateBlockId(): string {
    return `block_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}