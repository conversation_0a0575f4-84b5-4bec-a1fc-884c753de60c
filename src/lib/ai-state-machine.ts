// AI助手状态机定义
export type AIState = 
  | 'before-input'           // 初始状态：等待用户输入
  | 'input-on-focus'         // 聚焦状态：用户点击输入框
  | 'prompt-entered'         // 输入完成：用户填写了内容
  | 'submitting-state'       // 提交中：正在发送给AI
  | 'generating'             // 生成中：AI正在生成响应
  | 'generating-with-ai-question' // AI询问：AI提出问题等待用户选择
  | 'thread-continuation'    // Thread模式：用户选择继续对话
  | 'thread-input-filled'    // Thread输入：用户在Thread中输入了内容
  | 'prompt-sending-process' // Thread提交：Thread内容提交中
  | 'conversation-collapsed' // 对话折叠：内容已生成，对话被折叠
  | 'ai-generating-structured-content' // 结构化生成：AI生成结构化内容
  | 'generation-completed'   // 生成完成：最终状态

// 状态转换事件
export type AIEvent = 
  | 'FOCUS_INPUT'
  | 'ENTER_TEXT' 
  | 'BLUR_INPUT'
  | 'SUBMIT_PROMPT'
  | 'AI_START_GENERATING'
  | 'AI_ASK_QUESTION'
  | 'USER_SELECT_THREAD'
  | 'ENTER_THREAD_TEXT'
  | 'SUBMIT_THREAD'
  | 'COLLAPSE_CONVERSATION'
  | 'START_STRUCTURED_GENERATION'
  | 'COMPLETE_GENERATION'

// 应用数据状态
export interface AppState {
  // 当前AI状态
  currentState: AIState;
  
  // 用户输入数据
  userInput: string;
  threadInput: string;
  
  // AI生成的内容
  aiResponse: string;
  aiQuestion?: string;
  structuredContent?: StructuredContent;
  
  // UI状态
  isInputFocused: boolean;
  isSubmitting: boolean;
  isGenerating: boolean;
  isCollapsed: boolean;
  
  // 对话历史
  conversationHistory: ConversationItem[];
}

export interface ConversationItem {
  type: 'user' | 'ai' | 'question';
  content: string;
  timestamp: number;
}

export interface StructuredContent {
  title: string;
  sections: ContentSection[];
  codeBlocks: CodeBlock[];
}

export interface ContentSection {
  type: 'h1' | 'h2' | 'ul' | 'p';
  content: string;
  emoji?: string;
}

export interface CodeBlock {
  language: string;
  code: string;
  filename?: string;
}

// 状态转换逻辑
export class AIStateMachine {
  private state: AIState = 'before-input';
  private listeners: ((state: AIState) => void)[] = [];

  getCurrentState(): AIState {
    return this.state;
  }

  transition(event: AIEvent): void {
    const newState = this.getNextState(this.state, event);
    if (newState !== this.state) {
      this.state = newState;
      this.notifyListeners();
    }
  }

  subscribe(listener: (state: AIState) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.state));
  }

  private getNextState(currentState: AIState, event: AIEvent): AIState {
    const transitions: Record<AIState, Partial<Record<AIEvent, AIState>>> = {
      'before-input': {
        'FOCUS_INPUT': 'input-on-focus'
      },
      'input-on-focus': {
        'ENTER_TEXT': 'prompt-entered',
        'BLUR_INPUT': 'before-input'
      },
      'prompt-entered': {
        'SUBMIT_PROMPT': 'submitting-state',
        'BLUR_INPUT': 'before-input'
      },
      'submitting-state': {
        'AI_START_GENERATING': 'generating'
      },
      'generating': {
        'AI_ASK_QUESTION': 'generating-with-ai-question',
        'START_STRUCTURED_GENERATION': 'ai-generating-structured-content'
      },
      'generating-with-ai-question': {
        'USER_SELECT_THREAD': 'thread-continuation'
      },
      'thread-continuation': {
        'ENTER_THREAD_TEXT': 'thread-input-filled'
      },
      'thread-input-filled': {
        'SUBMIT_THREAD': 'prompt-sending-process'
      },
      'prompt-sending-process': {
        'COLLAPSE_CONVERSATION': 'conversation-collapsed'
      },
      'conversation-collapsed': {
        'START_STRUCTURED_GENERATION': 'ai-generating-structured-content'
      },
      'ai-generating-structured-content': {
        'COMPLETE_GENERATION': 'generation-completed'
      },
      'generation-completed': {}
    };

    return transitions[currentState][event] || currentState;
  }
}