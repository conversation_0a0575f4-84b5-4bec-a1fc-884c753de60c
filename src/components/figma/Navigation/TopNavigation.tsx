'use client';

interface TopNavigationProps {
  gid: string;
}

/**
 * 顶部工具栏组件 - 完全匹配Figma设计
 * 包含标题下拉菜单、搜索框和操作按钮
 */
export function TopNavigation({ gid }: TopNavigationProps) {
  return (
    <div className="box-border content-stretch flex flex-row items-center justify-start p-0 relative shrink-0 w-full h-10 px-4">
      {/* 标题下拉菜单 */}
      <div className="h-6 relative rounded-lg shrink-0 mr-2">
        <button className="h-full px-3 bg-transparent border border-neutral-600 rounded-lg text-neutral-300 text-sm font-medium hover:border-neutral-500 focus:outline-none focus:border-sky-500">
          Untitled
        </button>
      </div>

      {/* 分隔符 */}
      <div className="h-3 w-px bg-neutral-600 mr-2"></div>

      {/* 搜索框 */}
      <div className="h-6 relative rounded-lg shrink-0 w-80 mr-4">
        <div className="relative h-full">
          <img 
            src="http://localhost:3845/assets/8fbd0a653349f9d584b284e27b4b4dacc349b655.svg"
            alt="Search"
            className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3"
          />
          <input
            type="text"
            placeholder="Search"
            className="w-full h-full pl-7 pr-3 bg-neutral-700 border border-neutral-600 rounded-lg text-sm text-neutral-300 placeholder-neutral-500 focus:outline-none focus:border-sky-500"
          />
        </div>
      </div>

      {/* 操作按钮区域 */}
      <div className="flex items-center gap-2 ml-auto">
        {/* VS Code 按钮 */}
        <button className="bg-neutral-950 h-6 px-3 relative rounded-lg shrink-0 text-neutral-300 text-xs border border-neutral-700 hover:border-neutral-600 focus:outline-none focus:border-sky-500">
          Open with VS Code
        </button>

        {/* Preview 按钮 */}
        <button className="bg-sky-950 h-6 px-3 relative rounded-lg shrink-0 text-white text-xs border border-sky-800 hover:bg-sky-900 focus:outline-none focus:ring-1 focus:ring-sky-500 flex items-center gap-1">
          <img 
            src="http://localhost:3845/assets/77a2e8525b950f47935a890a3d0b0c634a6c8802.svg"
            alt=""
            className="w-3 h-3"
          />
          Preview
        </button>
      </div>
    </div>
  );
}