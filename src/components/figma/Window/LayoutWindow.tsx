'use client';

import { useState, useEffect } from 'react';
import { AIEvent, AppState, BlockState, BlockManager } from '@/lib/ai-state-machine';
import { TopNavigation } from '../Navigation/TopNavigation';
import { NavigationSidebar } from '../Sidebar/NavigationSidebar';
import { AIContentArea } from '../ContentArea/AIContentArea';

interface LayoutWindowProps {
  gid: string;
}

/**
 * 主窗口布局组件 - 完整的AI助手应用
 * 基于Figma设计实现固定布局 + 多块状态管理的动态内容
 */
export function LayoutWindow({ gid }: LayoutWindowProps) {
  // 块管理器实例
  const [blockManager] = useState(() => new BlockManager());
  const [appState, setAppState] = useState<AppState>(() => blockManager.getAppState());

  // 监听块管理器变化
  useEffect(() => {
    const unsubscribe = blockManager.subscribe((newAppState) => {
      setAppState(newAppState);
    });
    return unsubscribe;
  }, [blockManager]);

  // 状态转换处理
  const handleStateTransition = (blockId: string, event: AIEvent) => {
    blockManager.transitionBlockState(blockId, event);
  };

  // 更新块状态
  const updateBlockState = (blockId: string, updates: Partial<BlockState>) => {
    blockManager.updateBlockState(blockId, updates);
  };

  // 添加新块
  const handleAddBlock = () => {
    blockManager.createNewBlock();
  };

  // 切换侧边栏显示状态
  const toggleSidebar = () => {
    setAppState(prev => ({ ...prev, isCollapsed: !prev.isCollapsed }));
  };

  // 判断是否应该显示侧边栏
  const shouldShowSidebar = appState.hasGeneratingBlocks;

  return (
    <div className="min-h-screen bg-neutral-900">
      <div className="flex h-screen items-start overflow-hidden">
        {/* 
          主内容容器 - 使用mx-auto实现居中 
          - 侧边栏展开时: 固定宽度 1160px = 296(侧边栏) + 64(间距) + 800(内容)
          - 其他状态: 宽度自适应，最大宽度 864px = 800(内容) + 64(左边距), 通过pl-16实现左边距
        */}
        <div className={`flex h-full items-start mx-auto transition-all duration-300 ease-in-out ${
          (shouldShowSidebar && !appState.isCollapsed)
            ? 'w-[1160px]'
            : 'w-full max-w-[864px] pl-16'
        }`}>
          
          {/* 左侧边栏 - w-0 和 w-[296px] 控制显隐和动画 */}
          {shouldShowSidebar && (
            <div className={`flex-shrink-0 bg-neutral-900 transition-all duration-300 ease-in-out ${
              appState.isCollapsed ? 'w-0' : 'w-[296px]'
            }`}>
              {!appState.isCollapsed && (
                <NavigationSidebar
                  gid={gid}
                  appState={appState}
                  onToggleSidebar={toggleSidebar}
                />
              )}
            </div>
          )}

          {/* 侧边栏和内容区之间的间距 */}
          {shouldShowSidebar && !appState.isCollapsed && (
            <div className="w-16 flex-shrink-0" />
          )}

          {/* 内容区域 - flex-shrink-0 防止其在flex布局中被压缩 */}
          <div className="relative h-full w-full max-w-[800px] flex-shrink-0 bg-neutral-900">
            <AIContentArea
              gid={gid}
              appState={appState}
              onStateTransition={handleStateTransition}
              onUpdateState={updateBlockState}
              onAddBlock={handleAddBlock}
            />

            {/* 展开/收起按钮 - 仅在侧边栏可显示且已收起时出现 */}
            {shouldShowSidebar && appState.isCollapsed && (
              <button
                onClick={toggleSidebar}
                className="absolute left-[-48px] top-8 z-10 rounded-lg border border-neutral-600 p-2 bg-neutral-800 hover:border-neutral-500 transition-colors"
                aria-label="Expand sidebar"
              >
                <svg className="w-4 h-4 text-neutral-400" fill="currentColor" viewBox="0 0 16 16">
                  <path d="M1 2.5A1.5 1.5 0 0 1 2.5 1h3A1.5 1.5 0 0 1 7 2.5v3A1.5 1.5 0 0 1 5.5 7h-3A1.5 1.5 0 0 1 1 5.5v-3zm8 0A1.5 1.5 0 0 1 10.5 1h3A1.5 1.5 0 0 1 15 2.5v3A1.5 1.5 0 0 1 13.5 7h-3A1.5 1.5 0 0 1 9 5.5v-3zm-8 8A1.5 1.5 0 0 1 2.5 9h3A1.5 1.5 0 0 1 7 10.5v3A1.5 1.5 0 0 1 5.5 15h-3A1.5 1.5 0 0 1 1 13.5v-3zm8 0A1.5 1.5 0 0 1 10.5 9h3A1.5 1.5 0 0 1 15 10.5v3A1.5 1.5 0 0 1 13.5 15h-3A1.5 1.5 0 0 1 9 13.5v-3z"/>
                </svg>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}