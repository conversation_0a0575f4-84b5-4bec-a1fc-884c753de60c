/**
 * 窗口控制栏组件 - 基于Figma设计
 * 包含关闭、最小化、最大化按钮
 */
export function WindowControls() {
  return (
    <div className="bg-neutral-800 h-10 relative rounded-tl-[10px] rounded-tr-[10px] shrink-0 w-full">
      {/* 窗口控制按钮 */}
      <div className="h-10 relative shrink-0 w-20">
        {/* 模拟macOS风格的窗口控制按钮 */}
        <div className="flex items-center gap-2 p-3">
          <div className="w-3 h-3 rounded-full bg-red-500 hover:bg-red-600 cursor-pointer"></div>
          <div className="w-3 h-3 rounded-full bg-yellow-500 hover:bg-yellow-600 cursor-pointer"></div>
          <div className="w-3 h-3 rounded-full bg-green-500 hover:bg-green-600 cursor-pointer"></div>
        </div>
      </div>
    </div>
  );
}