'use client';

import { useRef, useEffect } from 'react';
import { AIState, AIEvent, BlockState, PromptResponseBlock as BlockData } from '@/lib/ai-state-machine';

interface PromptResponseBlockProps {
  block: BlockData;
  onStateTransition: (blockId: string, event: AIEvent) => void;
  onUpdateState: (blockId: string, updates: Partial<BlockState>) => void;
}

/**
 * 单个提示-响应块组件
 * 管理自己的状态和UI，完全独立于其他块
 */
export function PromptResponseBlock({ 
  block, 
  onStateTransition, 
  onUpdateState 
}: PromptResponseBlockProps) {
  const { id, state } = block;
  const { currentState } = state;
  
  // 输入区域的引用
  const inputAreaRef = useRef<HTMLDivElement>(null);

  // 处理输入框聚焦
  const handleInputFocus = () => {
    onStateTransition(id, 'FOCUS_INPUT');
    onUpdateState(id, { isInputFocused: true });
  };

  // 处理输入框失去焦点
  const handleInputBlur = () => {
    onStateTransition(id, 'BLUR_INPUT');
    onUpdateState(id, { isInputFocused: false, userInput: '' });
  };

  // 监听点击外部事件
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // 只在输入状态时检测点击外部，并且只有当输入框为空时才允许退出
      if ((currentState === 'input-on-focus' || currentState === 'prompt-entered') && 
          inputAreaRef.current && 
          !inputAreaRef.current.contains(event.target as Node) &&
          !state.userInput.trim()) {
        handleInputBlur();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [currentState, state.userInput, id]);

  // 处理文本输入
  const handleTextChange = (text: string) => {
    onUpdateState(id, { userInput: text });
    if (text.length > 0 && currentState === 'input-on-focus') {
      onStateTransition(id, 'ENTER_TEXT');
    }
  };

  // 处理提交
  const handleSubmit = () => {
    if (state.userInput.trim()) {
      onStateTransition(id, 'SUBMIT_PROMPT');
      onUpdateState(id, { isSubmitting: true });
      
      // 模拟AI响应
      setTimeout(() => {
        onStateTransition(id, 'AI_START_GENERATING');
        onUpdateState(id, { 
          isSubmitting: false, 
          isGenerating: true,
          aiResponse: "Develop a cross-platform Todo application using SQLite for local data storage and Passkey for secure, passwordless authentication..."
        });
      }, 1000);
    }
  };

  // 根据当前状态渲染对应的内容
  function renderStateContent() {
    switch (currentState) {
      case 'before-input':
        return renderBeforeInputState();
      
      case 'input-on-focus':
      case 'prompt-entered':
        return renderInputFocusState();
      
      case 'submitting-state':
        return renderSubmittingState();
      
      case 'generating':
        return renderGeneratingState();
      
      default:
        return renderBeforeInputState();
    }
  }

  // 状态1: 初始输入状态
  function renderBeforeInputState() {
    return (
      <div className="w-full relative">
        {/* Block容器 - 响应式宽度，相对定位，flex布局确保同一行 */}
        <div ref={inputAreaRef} className="relative shrink-0 w-full h-6 flex items-center">
          {/* MenuTrigger - 左侧图标组 */}
          <div className="bg-neutral-950 h-6 rounded-lg px-1 flex items-center gap-1 mr-2">
            {/* TriggerIcon1 - 主触发图标 */}
            <div className="w-4 h-4 flex items-center justify-center">
              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 12 12">
                <rect x="1" y="2" width="10" height="1.5" rx="0.75"/>
                <rect x="1" y="5.25" width="10" height="1.5" rx="0.75"/>
                <rect x="1" y="8.5" width="10" height="1.5" rx="0.75"/>
              </svg>
            </div>
            
            {/* TriggerIcon2 - 辅助图标 */}
            <div className="w-4 h-4 flex items-center justify-center">
              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 12 12">
                <path d="M6 1a5 5 0 1 0 0 10A5 5 0 0 0 6 1zM4.5 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0z"/>
              </svg>
            </div>
          </div>
          
          {/* TextContainer - 文本输入区域，flex-1占据剩余空间 */}
          <div 
            className="flex-1 relative cursor-text h-6 flex items-center"
            onClick={handleInputFocus}
          >
            <p className="font-inter font-normal text-[16px] text-left text-neutral-600">
              Write, press 'space' for AI
            </p>
            {/* 光标 - 绝对定位 */}
            {state.isInputFocused && (
              <div className="absolute h-4 left-1 top-1 w-px bg-white animate-pulse"></div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // 状态2-3: 输入框聚焦和输入完成状态
  function renderInputFocusState() {
    const isPromptEntered = currentState === 'prompt-entered';
    
    return (
      <div className="w-full relative">
        <div
          ref={inputAreaRef}
          className={`bg-neutral-950 relative rounded-2xl shrink-0 w-full p-4 border-2 ${
            state.isInputFocused ? 'border-sky-500' : 'border-transparent'
          }`}
        >
          {/* 文本输入容器 */}
          <div className="box-border content-stretch flex flex-row items-center justify-start p-0 relative shrink-0 w-full mb-4">
            <input
              type="text"
              value={state.userInput}
              onChange={(e) => handleTextChange(e.target.value)}
              onFocus={handleInputFocus}
              placeholder="Tell me what you want to do"
              className="flex-1 bg-transparent border-none outline-none text-[16px] text-neutral-300 placeholder-neutral-600 font-inter font-normal"
              autoFocus={state.isInputFocused}
            />
          </div>

          {/* 按钮容器 */}
          <div className="box-border content-stretch flex flex-row gap-2 items-start justify-start p-0 relative shrink-0 w-full">
            {/* 操作按钮 1 - Plus 图标 */}
            <button className="relative rounded-lg shrink-0 border border-neutral-600 p-2 w-8 h-8 flex items-center justify-center hover:border-neutral-500">
              <svg className="w-4 h-4 text-neutral-400" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
              </svg>
            </button>

            {/* 操作按钮 2 - Attachment 图标 */}
            <button className="relative rounded-lg shrink-0 border border-neutral-700 p-2 w-8 h-8 flex items-center justify-center hover:border-neutral-600">
              <svg className="w-4 h-4 text-neutral-400" fill="currentColor" viewBox="0 0 16 16">
                <path d="M4.5 3a2.5 2.5 0 0 1 5 0v9a1.5 1.5 0 0 1-3 0V5a.5.5 0 0 1 1 0v7a.5.5 0 0 0 1 0V3a1.5 1.5 0 1 0-3 0v9a2.5 2.5 0 0 0 5 0V5a.5.5 0 0 1 1 0v7a3.5 3.5 0 1 1-7 0V3z"/>
              </svg>
            </button>

            {/* 弹性空间 */}
            <div className="basis-0 grow min-h-px min-w-px"></div>

            {/* 提交按钮 */}
            <button
              onClick={handleSubmit}
              disabled={!state.userInput.trim() || state.isSubmitting}
              className={`relative rounded-lg shrink-0 p-2 w-8 h-8 flex items-center justify-center ${
                state.userInput.trim() && !state.isSubmitting
                  ? 'bg-gradient-to-r from-sky-500 to-blue-500 border-sky-400'
                  : 'bg-neutral-800 border-neutral-700'
              } border transition-all duration-200`}
            >
              {state.isSubmitting ? (
                <div className="w-3 h-3 border border-neutral-300 border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 16 16">
                  <path d="m12.14 8.753-5.482 4.796c-.646.566-1.658.106-1.658-.753V3.204a1 1 0 0 1 1.659-.753l5.48 4.796a1 1 0 0 1 0 1.506z"/>
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 状态4: 提交中状态
  function renderSubmittingState() {
    return (
      <div className="relative shrink-0 w-full">
        <div className="bg-neutral-950 relative rounded-2xl shrink-0 w-full p-4">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 animate-spin">
              <svg className="w-4 h-4 text-neutral-400" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="m12 2 A10 10 0 0 1 22 12 h-4 A6 6 0 0 0 12 6 V2 Z"></path>
              </svg>
            </div>
            <p className="font-inter font-normal text-[16px] text-neutral-400">
              Submitting your request...
            </p>
          </div>
        </div>
      </div>
    );
  }

  // 状态5: 生成中状态
  function renderGeneratingState() {
    return (
      <div className="relative shrink-0 w-full space-y-6">
        {/* 用户输入块 */}
        <div className="relative shrink-0 w-full">
          <div className="bg-neutral-950 relative rounded-2xl shrink-0 w-full">
            {/* Prompt 信息头部 */}
            <div className="flex flex-row gap-1 items-start justify-between p-4 w-full mb-2">
              <p className="font-inter font-medium leading-[18px] not-italic text-[14px] text-left text-neutral-600">
                Prompt:
              </p>
              <div className="flex flex-row gap-1 items-center justify-start">
                <p className="font-inter font-medium leading-[18px] not-italic relative shrink-0 text-[14px] text-left text-neutral-50 text-nowrap">
                  Worked for 1m 22s
                </p>
                <svg className="overflow-clip relative shrink-0 size-4 text-neutral-400" fill="currentColor" viewBox="0 0 16 16">
                  <path d="m12.14 8.753-5.482 4.796c-.646.566-1.658.106-1.658-.753V3.204a1 1 0 0 1 1.659-.753l5.48 4.796a1 1 0 0 1 0 1.506z"/>
                </svg>
              </div>
            </div>

            {/* Prompt 文本 */}
            <div className="p-4 pt-0">
              <p className="font-inter font-normal leading-[28px] not-italic relative shrink-0 text-[16px] text-left text-neutral-400 w-full">
                {state.userInput}
              </p>
            </div>
          </div>
        </div>

        {/* 代码生成块 */}
        <div className="relative shrink-0 w-full">
          <div className="w-full relative">
            <div className="bg-neutral-950 relative rounded-2xl shrink-0 w-full">
              {/* 代码头部 */}
              <div className="h-12 relative shrink-0 w-full p-4 flex items-center justify-between">
                <div className="flex flex-row gap-1 items-center justify-start">
                  {/* 加载图标 */}
                  <div className="overflow-clip relative shrink-0 size-6 mr-2">
                    <div 
                      className="absolute left-1/2 overflow-clip size-4 top-1/2"
                      style={{ transform: 'translate(-50%, -50%) rotate(90deg)' }}
                    >
                      <svg className="w-4 h-4 animate-spin text-neutral-400" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="m12 2 A10 10 0 0 1 22 12 h-4 A6 6 0 0 0 12 6 V2 Z"></path>
                      </svg>
                    </div>
                  </div>
                  
                  <p className="font-inter font-medium leading-[18px] not-italic relative shrink-0 text-[14px] text-left text-neutral-50 text-nowrap">
                    Build a todo app...
                  </p>
                </div>
                
                <svg className="overflow-clip relative shrink-0 size-4 text-neutral-400" fill="currentColor" viewBox="0 0 16 16">
                  <path d="m12.14 8.753-5.482 4.796c-.646.566-1.658.106-1.658-.753V3.204a1 1 0 0 1 1.659-.753l5.48 4.796a1 1 0 0 1 0 1.506z"/>
                </svg>
              </div>
              
              {/* 生成的内容 */}
              <div className="relative shrink-0 w-full p-4">
                <p className="font-inter font-normal leading-[28px] min-w-full not-italic relative shrink-0 text-[16px] text-left text-neutral-50">
                  {state.aiResponse}
                  {/* 文本光标 */}
                  <span className="inline-block w-2 h-4 bg-neutral-50 ml-1 animate-pulse"></span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative shrink-0 w-full mb-6">
      {renderStateContent()}
    </div>
  );
}
