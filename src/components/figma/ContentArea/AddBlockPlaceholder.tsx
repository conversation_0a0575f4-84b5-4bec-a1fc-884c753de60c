'use client';

interface AddBlockPlaceholderProps {
  onAddBlock: () => void;
}

/**
 * 添加新块的占位符组件
 * 显示一个可点击的区域来创建新的提示-响应块
 */
export function AddBlockPlaceholder({ onAddBlock }: AddBlockPlaceholderProps) {
  return (
    <div className="w-full relative">
      <div
        className="w-full relative cursor-pointer group hover:bg-neutral-800/30 rounded-lg transition-colors p-4"
        onClick={onAddBlock}
      >
        {/* Block容器 - 响应式宽度，相对定位，flex布局确保同一行 */}
        <div className="relative shrink-0 w-full h-6 flex items-center">
          {/* MenuTrigger - 左侧图标组 */}
          <div className="bg-neutral-950 h-6 rounded-lg px-1 flex items-center gap-1 mr-2 group-hover:bg-neutral-800 transition-colors">
            {/* TriggerIcon1 - 主触发图标 */}
            <div className="w-4 h-4 flex items-center justify-center">
              <svg className="w-3 h-3 text-neutral-600 group-hover:text-neutral-400 transition-colors" fill="currentColor" viewBox="0 0 12 12">
                <rect x="1" y="2" width="10" height="1.5" rx="0.75"/>
                <rect x="1" y="5.25" width="10" height="1.5" rx="0.75"/>
                <rect x="1" y="8.5" width="10" height="1.5" rx="0.75"/>
              </svg>
            </div>
            
            {/* TriggerIcon2 - 辅助图标 */}
            <div className="w-4 h-4 flex items-center justify-center">
              <svg className="w-3 h-3 text-neutral-600 group-hover:text-neutral-400 transition-colors" fill="currentColor" viewBox="0 0 12 12">
                <path d="M6 1a5 5 0 1 0 0 10A5 5 0 0 0 6 1zM4.5 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0z"/>
              </svg>
            </div>
          </div>
          
          {/* TextContainer - 文本输入区域，flex-1占据剩余空间 */}
          <div className="flex-1 relative h-6 flex items-center">
            <p className="font-inter font-normal text-[16px] text-left text-neutral-700 group-hover:text-neutral-500 transition-colors">
              Write, press 'space' for AI
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
