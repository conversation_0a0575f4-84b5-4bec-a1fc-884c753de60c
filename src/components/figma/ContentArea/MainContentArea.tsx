'use client';

import { AIEvent, AppState, BlockState } from '@/lib/ai-state-machine';
import { PromptResponseBlock } from './PromptResponseBlock';
import { AddBlockPlaceholder } from './AddBlockPlaceholder';

interface MainContentAreaProps {
  gid: string;
  appState: AppState;
  onStateTransition: (blockId: string, event: AIEvent) => void;
  onUpdateState: (blockId: string, updates: Partial<BlockState>) => void;
  onAddBlock: () => void;
}

/**
 * 主内容区域组件 - 基于Figma设计实现真实的AI交互界面
 * 现在支持多个独立的提示-响应块
 */
export function MainContentArea({
  gid,
  appState,
  onStateTransition,
  onUpdateState,
  onAddBlock
}: MainContentAreaProps) {
  // 判断是否应该显示侧边栏
  const shouldShowSidebar = appState.hasGeneratingBlocks;



  return (
    <div className={`w-full h-full flex flex-col items-start justify-start min-h-screen ${
      shouldShowSidebar && !appState.isCollapsed
        ? 'pl-[40px]'
        : ''
    }`}>
      {/* 调试信息 */}
      <div className="text-white text-sm p-2 bg-neutral-800 rounded mb-4">
        Blocks: {appState.blocks.length} | Sidebar: {shouldShowSidebar ? 'Yes' : 'No'} | Collapsed: {appState.isCollapsed ? 'Yes' : 'No'}
      </div>

      {/* 内容区域 - 包含所有块 */}
      <div className="relative w-full mt-8">
        {/* 渲染所有现有的块 */}
        {appState.blocks.map((block) => (
          <PromptResponseBlock
            key={block.id}
            block={block}
            onStateTransition={onStateTransition}
            onUpdateState={onUpdateState}
          />
        ))}

        {/* 添加新块的占位符 */}
        <AddBlockPlaceholder onAddBlock={onAddBlock} />
      </div>
    </div>
  );








}