'use client';

import { AIEvent, AppState, BlockState } from '@/lib/ai-state-machine';
import { MainContentArea } from './MainContentArea';

interface AIContentAreaProps {
  gid: string;
  appState: AppState;
  onStateTransition: (blockId: string, event: AIEvent) => void;
  onUpdateState: (blockId: string, updates: Partial<BlockState>) => void;
  onAddBlock: () => void;
}

/**
 * AI内容区域组件 - 基于多块状态管理的动态内容渲染
 * 支持多个独立的提示-响应块
 */
export function AIContentArea({
  gid,
  appState,
  onStateTransition,
  onUpdateState,
  onAddBlock
}: AIContentAreaProps) {
  return (
    <MainContentArea
      gid={gid}
      appState={appState}
      onStateTransition={onStateTransition}
      onUpdateState={onUpdateState}
      onAddBlock={onAddBlock}
    />
  );
}