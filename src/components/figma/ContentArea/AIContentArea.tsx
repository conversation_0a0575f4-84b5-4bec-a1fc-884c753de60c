'use client';

import { AIState, AIEvent, AppState } from '@/lib/ai-state-machine';
import { MainContentArea } from './MainContentArea';

interface AIContentAreaProps {
  gid: string;
  currentState: AIState;
  appState: AppState;
  onStateTransition: (event: AIEvent) => void;
  onUpdateState: (updates: Partial<AppState>) => void;
}

/**
 * AI内容区域组件 - 基于状态机驱动的动态内容渲染
 * 根据当前AI状态渲染对应的UI界面
 */
export function AIContentArea({ 
  gid, 
  currentState, 
  appState, 
  onStateTransition, 
  onUpdateState 
}: AIContentAreaProps) {
  return (
    <MainContentArea
      gid={gid}
      currentState={currentState}
      appState={appState}
      onStateTransition={onStateTransition}
      onUpdateState={onUpdateState}
    />
  );
}