'use client';

import { AIState, AppState } from '@/lib/ai-state-machine';

interface NavigationSidebarProps {
  gid: string;
  currentState: AIState;
  appState: AppState;
  onToggleSidebar: () => void;
}

/**
 * 侧边栏导航组件 - 基于Figma设计实现
 * 显示当前文档的内容结构和生成状态
 */
export function NavigationSidebar({ gid, currentState, appState, onToggleSidebar }: NavigationSidebarProps) {
  // Mock数据：模拟文档目录结构
  const mockContent = [
    { 
      title: "Build a Todo App with SQLite and Passkey Authentication", 
      status: currentState === 'before-input' ? 'pending' : 'generating',
      isActive: true 
    }
  ];

  // 只在生成状态显示侧边栏
  const shouldShowSidebar = currentState === 'generating' || 
                           currentState === 'generating-with-ai-question' ||
                           currentState === 'ai-generating-structured-content' ||
                           currentState === 'generation-completed';

  if (!shouldShowSidebar) {
    return null;
  }

  return (
    <div className="w-[296px] h-full flex flex-col" style={{ padding: '32px 0px 32px 40px' }}>
      {/* 目录图标按钮 - 添加点击收起功能 */}
      <div className="mb-4">
        <button 
          onClick={onToggleSidebar}
          className="relative rounded-lg shrink-0 border border-neutral-600 p-2 hover:border-neutral-500 transition-colors"
        >
          <svg className="w-4 h-4 text-neutral-400" fill="currentColor" viewBox="0 0 16 16">
            <path d="M1 2.5A1.5 1.5 0 0 1 2.5 1h3A1.5 1.5 0 0 1 7 2.5v3A1.5 1.5 0 0 1 5.5 7h-3A1.5 1.5 0 0 1 1 5.5v-3zm8 0A1.5 1.5 0 0 1 10.5 1h3A1.5 1.5 0 0 1 15 2.5v3A1.5 1.5 0 0 1 13.5 7h-3A1.5 1.5 0 0 1 9 5.5v-3zm-8 8A1.5 1.5 0 0 1 2.5 9h3A1.5 1.5 0 0 1 7 10.5v3A1.5 1.5 0 0 1 5.5 15h-3A1.5 1.5 0 0 1 1 13.5v-3zm8 0A1.5 1.5 0 0 1 10.5 9h3A1.5 1.5 0 0 1 15 10.5v3A1.5 1.5 0 0 1 13.5 15h-3A1.5 1.5 0 0 1 9 13.5v-3z"/>
          </svg>
        </button>
      </div>

      {/* 内容项目 */}
      <div className="flex-1">
        <div className="box-border content-stretch flex flex-row gap-1 h-[21px] items-center justify-start p-0 relative rounded-lg shrink-0 w-full">
          {/* 加载图标 */}
          <div className="overflow-clip relative shrink-0 size-4">
            {currentState === 'generating' || currentState === 'ai-generating-structured-content' ? (
              <div 
                className="absolute left-1/2 overflow-clip size-4 top-1/2 transform -translate-x-1/2 -translate-y-1/2"
                style={{ transform: 'translate(-50%, -50%) rotate(90deg)' }}
              >
                <svg className="w-3 h-3 animate-spin text-neutral-400" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="m12 2 A10 10 0 0 1 22 12 h-4 A6 6 0 0 0 12 6 V2 Z"></path>
                </svg>
              </div>
            ) : (
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            )}
          </div>
          
          {/* 目录文本 */}
          <p className="basis-0 font-inter font-medium grow leading-[18px] min-h-px min-w-px not-italic overflow-ellipsis overflow-hidden relative shrink-0 text-[14px] text-left text-neutral-600 text-nowrap">
            {mockContent[0].title}
          </p>
        </div>
      </div>
    </div>
  );
}