/**
 * 加载动画组件 - 基于Figma设计的旋转加载图标
 * 初始角度90度，顺时针连续旋转
 */
export function LoadingSpinner({ className = "" }: { className?: string }) {
  return (
    <div className={`flex items-center justify-center min-h-screen bg-neutral-900 ${className}`}>
      <div className="relative">
        {/* 基于Figma JSON中的加载图标 */}
        <div 
          className="w-4 h-4 animate-loading-spin"
          style={{
            transform: 'rotate(90deg)',
            backgroundImage: 'url(/assets/loading-icon.svg)',
            backgroundSize: 'contain',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center',
          }}
        />
      </div>
    </div>
  );
}