{"permissions": {"allow": ["WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(claude mcp:*)", "mcp__figma-dev-mode-mcp-server__get_variable_defs", "mcp__figma-dev-mode-mcp-server__get_code_connect_map", "mcp__figma-dev-mode-mcp-server__get_code", "Bash(npx create-next-app:*)", "Bash(npm install)", "Bash(npm run build)", "Bash(npm run dev)", "Bash(npm run type-check:*)", "Bash(kill:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(true)"], "deny": []}}