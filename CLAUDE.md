# CLAUDE.md

This file contains information about the project that will be useful for <PERSON> to understand and work with the codebase.

## AI Code Generation Interface - Complete Workflow Documentation

### Project Overview
This is a comprehensive AI-powered code generation interface with 8 distinct states representing a complete user workflow from initial prompt input through multi-turn conversation.

### Page Flow and Interactions

#### Page 1: Initial Interface (Left Panel)
- **File**: `figma-complete-data.json`
- **Function**: Base interface with component specifications
- **Components**: Left panel, search field, action buttons
- **State**: Initial/Empty state

#### Page 2: Input on Focus
- **File**: `page-2-input-on-focus.json`
- **Function**: Input field activation
- **Interactions**: 
  - Input field gains focus with blue border (#0ea5e9)
  - <PERSON><PERSON>or appears in input area
  - Submit button remains inactive (gray background #262626)
- **Transition**: User clicks on input field

#### Page 3: Prompt Entered
- **File**: `page-3-prompt-entered.json`
- **Function**: User has typed prompt but not submitted
- **Interactions**:
  - Input contains text: "Todo App with SQLite & Passkey"
  - Submit button activates with blue gradient background
  - Caret visible at end of text
- **Transition**: User types text into input field

#### Page 4: Submitting State
- **File**: `page-4-submitting-state.json`
- **Function**: Transition state during submission
- **Interactions**:
  - Text color changes to disabled (#404040)
  - Action buttons disabled with 30% opacity
  - Submit button shows loading spinner animation
  - Blue focus border remains active
  - All UI interactions disabled
- **Transition**: User clicks submit button

#### Page 5: Generating Response
- **File**: `page-5-generating.json`
- **Function**: AI generating response with live updates
- **Features**:
  - Prompt completion panel shows execution time "Worked for 1m 22s"
  - Code generation block with loading animation
  - Live text streaming with typewriter effect
  - Sidebar catalogue with current item highlighted
  - Gradient title text effects
- **Interactions**: Response streams in real-time

#### Page 6: AI Question State
- **File**: `page-6-generating-with-ai-question.json`
- **Function**: AI asks clarifying question
- **Features**:
  - Blue highlighted question area
  - Three action buttons: Accept, Thread, Re-run
  - Generated content remains visible
  - User can choose interaction type
- **Interactions**: User selects response action

#### Page 7: Thread Continuation
- **File**: `page-7-thread-continuation.json`
- **Function**: After selecting "Thread" - new conversation context
- **Features**:
  - Previous generation completed and visible
  - New input dialog appears (similar to initial state)
  - Maintains conversation context
  - Ready for follow-up questions
- **Transition**: User selected "Thread" from page 6

#### Page 8: Thread Input Active
- **File**: `page-8-thread-input-filled.json`
- **Function**: User typing in thread continuation
- **Features**:
  - Input contains: "I'd like to use Vue.js for the frontend and Node.js with Express for the backend."
  - Submit button activated with blue gradient
  - Demonstrates button state differences
  - Focused input with blue border
- **Interactions**: Ready for next submission

#### Page 9: Prompt Sending Process
- **File**: `page-9-prompt-sending-process.json`
- **Function**: Thread reply submission in progress
- **Features**:
  - Complete conversation history visible
  - AI question area with blue highlighting (#082f49)
  - Thread reply in submitting state with disabled text (#404040)
  - Multiple loading animations (header, sidebar, submit button)
  - Focus border maintained during processing
- **Transition**: User submitted tech stack choice

#### Page 10: Conversation Collapsed
- **File**: `page-10-conversation-collapsed.json`
- **Function**: Post-send success with collapsed conversation view
- **Features**:
  - Conversation data collapsed but preserved
  - Click-to-expand functionality for prompt and code panels
  - Clean compact interface
  - Continued loading animations
  - Multi-turn conversation context hidden but accessible
- **Interactions**: Expandable conversation history

#### Page 11: AI Generating Structured Content
- **File**: `page-11-ai-generating-structured-content.json`
- **Function**: AI generating structured content with multiple content types
- **Features**:
  - Hierarchical content: main title (h1, 32px), section titles (h2, 28px)
  - Bulleted list with tech stack details (Vue 3, Node.js, SQLite, Passkey)
  - Emoji integration in section titles (🔧, 🗂)
  - Hierarchical sidebar navigation reflecting content structure
  - Active page indicator in sidebar (#0ea5e9)
  - Collapsed sub-conversation block at bottom
- **Content Types**: Structured document generation with semantic HTML

#### Page 12: Generation Completed
- **File**: `page-12-generation-completed.json`
- **Function**: Generation finished - complete structured content displayed
- **Features**:
  - Expanded prompt panel with execution time "Worked for 1m 22s"
  - Complete project structure code block with syntax highlighting
  - Menlo font for code (14px), color-coded directory tree
  - Completion status sidebar with success icon (✓)
  - All sections completed, no loading animations
  - Language selector dropdown (Bash) in code block
- **Final State**: All content generated and displayed with proper completion indicators

### Key Design Patterns

#### State Management
- **Inactive State**: Gray background (#262626), no user interaction
- **Active State**: Blue gradient, ready for submission
- **Loading State**: Spinner animation, disabled interactions
- **Disabled State**: 30% opacity, non-interactive

#### Visual Feedback
- **Focus Indicators**: Blue border (#0ea5e9) for active elements
- **Loading Animations**: Rotating spinners at 90� initial rotation
- **Gradient Effects**: Linear gradients for active states and titles
- **Text States**: White (#fafafa) active, gray (#404040) disabled

#### Interaction Patterns
- **Progressive Enhancement**: Empty � Focused � Filled � Submitted � Generated
- **Multi-turn Conversation**: Thread continuation with context preservation
- **Real-time Feedback**: Live text generation with streaming effects
- **Accessible States**: Clear visual indicators for all interaction states

### Technical Implementation Requirements

#### Framework Stack
- **Frontend**: React with TypeScript
- **Styling**: Tailwind CSS (with vanilla CSS fallback)
- **State Management**: Component-level state for UI interactions
- **Animations**: CSS animations for loading states and transitions

#### Key Components
1. **Window Container**: Main application window with controls
2. **Input Field**: Multi-state input with focus/active/disabled states
3. **Submit Button**: Inactive/active/loading state variations
4. **Response Area**: Live text generation with streaming
5. **Action Buttons**: Accept/Thread/Re-run interaction options
6. **Loading Spinner**: Rotating animation for processing states

#### Accessibility Requirements
- Keyboard navigation support
- Screen reader announcements for state changes
- ARIA labels for loading states
- Color contrast compliance
- Semantic HTML structure
- Live region updates for text generation

### Asset Management
- All images served from localhost:3845/assets/
- SVG icons for UI elements
- Consistent icon sizing (16px standard, 12px for loading)
- Window control buttons, search icons, action buttons

### Design Tokens
- **Colors**: Neutral palette with blue accent (#0ea5e9)
- **Typography**: Inter font family, multiple weights
- **Spacing**: 8px base unit, consistent padding/margins
- **Borders**: Rounded corners (8px-18px), subtle shadows
- **Effects**: Opacity transitions, gradient backgrounds

This documentation serves as the complete specification for implementing the AI code generation interface with all state transitions and interaction patterns preserved.