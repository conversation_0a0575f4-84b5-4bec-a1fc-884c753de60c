import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  // 支持任意值的安全列表
  safelist: [
    // 动态宽度
    'w-[296px]',
    'w-[800px]', 
    'w-[320px]',
    // 动态圆角
    'rounded-[10px]',
    'rounded-[18px]',
    // 动态间距
    'p-[32px_0px_32px_40px]',
    // 动态阴影
    'shadow-[0px_25px_50px_0px_rgba(0,0,0,0.5)]',
    // 基础样式类
    'bg-neutral-900',
    'bg-neutral-800', 
    'bg-neutral-950',
    'text-neutral-50',
    'text-neutral-400',
    'text-neutral-600',
    'border-neutral-700',
    'border-neutral-600',
    'text-sky-400',
    'bg-sky-500',
    'hover:bg-neutral-600',
    'hover:border-neutral-600',
  ],
  theme: {
    extend: {
      // 自定义断点 - 精确控制布局切换时机
      screens: {
        'layout': '1096px',  // 侧边栏296px + 内容区800px = 1096px时切换
      },
      // 基于Figma JSON数据的精确颜色定义
      colors: {
        neutral: {
          50: '#fafafa',   // 主要文本色
          400: '#a3a3a3',  // 次要文本色  
          600: '#525252',  // 禁用文本色
          800: '#262626',  // 侧边栏背景
          900: '#171717',  // 页面背景
          950: '#0a0a0a',  // 块背景
        },
        sky: {
          500: '#0ea5e9',  // 品牌色/焦点边框
          950: '#082f49',  // 主按钮背景
        },
        green: {
          400: '#4ade80',  // 成功状态色
        }
      },
      // 基于Figma的精确宽度定义
      width: {
        '[296px]': '296px',  // 侧边栏固定宽度
        '[800px]': '800px',  // 内容区域最大宽度
        '[320px]': '320px',  // 搜索框宽度
      },
      // 基于Figma的字体配置
      fontFamily: {
        'inter': ['Inter', 'sans-serif'],
        'menlo': ['Menlo', 'Monaco', 'Consolas', 'monospace'],
      },
      // 基于Figma的精确圆角
      borderRadius: {
        '[10px]': '10px',
        '[18px]': '18px',
      },
      // 基于Figma的精确阴影
      boxShadow: {
        'figma-window': '0px 25px 50px 0px rgba(0,0,0,0.5), 0px 0px 0px 1px #404040',
      },
      // 基于Figma的精确间距
      spacing: {
        '[32px]': '32px',
        '[40px]': '40px',
      },
    },
  },
  plugins: [],
}

export default config