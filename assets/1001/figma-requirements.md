# Figma Design Requirements - Left Panel Component

## 概述
这是一个左侧面板组件，包含文本输入区域和菜单触发器。主要用于AI辅助写作功能。

## 组件结构

### 1. 主容器 (LeftPanel)
- **背景色**: `#171717` (深灰色背景)
- **尺寸**: 全屏高度
- **边框**: 右侧1px实线边框，颜色 `#404040`

### 2. 内容区块 (Block)
- **宽度**: 800px
- **内边距**: 水平64px，垂直48px
- **布局**: 水平居中

### 3. 文本容器 (Text Container)
- **功能**: 文本输入区域
- **占位符文本**: "Write, press 'space' for AI"
- **字体**: Inter Regular
- **字号**: 16px
- **行高**: 1.75
- **文本颜色**: `#525252` (较淡的灰色)
- **光标**: 1px宽，16px高，颜色 `#fafafa`

### 4. 菜单触发器 (<PERSON>u Trigger)
- **背景色**: `#0a0a0a` (深黑色)
- **边框**: 1px实线，颜色 `#404040`
- **圆角**: 8px
- **高度**: 24px
- **内边距**: 4px
- **位置**: 绝对定位，左侧2px处

#### 触发器内容
- **图标**: 16x16px尺寸
- **图标颜色**: `#fafafa` (白色)
- **分隔符**: 旋转90度的线条
- **拖拽图标**: 左对齐

## 设计变量 (Design Tokens)

| 变量名 | 值 | 用途 |
|--------|-----|------|
| `Text/text-regular-subtlest` | `#525252` | 次要文本颜色 |
| `Text/text-caret` | `#fafafa` | 光标颜色 |
| `Fill/fill-regular` | `#fafafa` | 常规填充色 |
| `Border/border-divider` | `#404040` | 分割线边框色 |
| `Base/button` | `#0a0a0a` | 按钮背景色 |
| `Base/edge` | `#404040` | 边缘色 |
| `Background/bg-background` | `#171717` | 背景色 |

## 资源文件

### 图标资源
- **主图标**: `http://localhost:3845/assets/7ca200174db898d1413376ff5c52683a0c7ee762.svg`
- **矢量图标**: `http://localhost:3845/assets/9ed89d85bec2b3381202c836e8be1ae5355efd54.svg`
- **次要图标**: `http://localhost:3845/assets/42235bc65995b3bc46ec09f463c5afcb920abd05.svg`

## 技术规范

### React组件
- 使用TypeScript接口定义props
- 支持图标自定义和类型状态
- Tailwind CSS样式类
- 响应式设计考虑

### 样式要求
- 如果项目未使用Tailwind，需转换为原生CSS
- 不添加额外依赖项
- 保持现有代码库风格一致

## 交互行为

### 输入框
- 支持文本输入
- 按空格键触发AI功能
- 显示文本光标

### 菜单触发器
- 包含多个操作图标
- 支持悬停状态变化
- 可拖拽功能

## 布局特性
- 垂直居中布局
- 固定宽度内容区
- 响应式图标排列
- 灵活的组件组合

## 开发注意事项
1. 图标资源从本地服务器加载
2. 组件支持属性自定义
3. 保持设计系统一致性
4. 考虑无障碍访问性