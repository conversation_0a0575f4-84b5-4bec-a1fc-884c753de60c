{"component": {"name": "Window", "id": "node-2197_1857", "type": "Application Window", "description": "完整的应用程序窗口，包含顶部控制栏、搜索栏、按钮和聚焦状态的输入区域"}, "structure": {"hierarchy": [{"name": "Window", "id": "node-2197_1857", "type": "div", "className": "bg-neutral-800 box-border content-stretch flex flex-col gap-px items-start justify-start overflow-clip p-0 relative rounded-[10px] shadow-[0px_25px_50px_0px_rgba(0,0,0,0.5),0px_0px_0px_1px_#404040] size-full", "children": [{"name": "WindowControls", "id": "node-2197_1858", "type": "div", "className": "bg-neutral-800 h-10 relative rounded-tl-[10px] rounded-tr-[10px] shrink-0 w-full", "children": [{"name": "ControlButtons", "id": "node-2197_1859", "type": "img", "className": "h-10 relative shrink-0 w-20", "src": "http://localhost:3845/assets/a8c1933f48906c52d8dcdebde6940ce54b9b153f.svg"}, {"name": "TopBar", "id": "node-2197_1863", "type": "div", "className": "basis-0 grow h-full min-h-px min-w-px relative shrink-0", "children": [{"name": "Dropdown", "id": "node-2197_1864", "type": "Dropdown", "className": "h-6 relative rounded-lg shrink-0", "props": {"labelText": "Untitled", "showIcon": "Off"}}, {"name": "Separator", "id": "node-2197_1865", "type": "img", "className": "h-3 relative shrink-0 w-0", "src": "http://localhost:3845/assets/a066c61403e8d3c3b89c3fe9ed2713330604d847.svg"}, {"name": "TextField", "id": "node-2197_1866", "type": "div", "className": "h-6 relative rounded-lg shrink-0 w-80", "children": [{"name": "SearchIcon", "id": "node-I2197_1866-2078_500", "type": "img", "src": "http://localhost:3845/assets/8fbd0a653349f9d584b284e27b4b4dacc349b655.svg"}, {"name": "SearchText", "id": "node-I2197_1866-2078_501", "type": "p", "text": "Search", "className": "font-['Inter:Regular',_sans-serif] font-normal leading-[0] not-italic relative shrink-0 text-[12px] text-left text-neutral-600 text-nowrap"}]}]}, {"name": "ActionButtons", "id": "node-2255_6046", "type": "div", "children": [{"name": "VSCodeButton", "id": "node-2255_6047", "type": "button", "className": "bg-neutral-950 h-6 relative rounded-lg shrink-0", "text": "Open with VS Code"}, {"name": "PreviewButton", "id": "node-2255_6048", "type": "button", "className": "bg-sky-950 h-6 relative rounded-lg shrink-0", "text": "Preview", "icon": "http://localhost:3845/assets/77a2e8525b950f47935a890a3d0b0c634a6c8802.svg"}]}]}, {"name": "MainContent", "id": "node-2197_1867", "type": "div", "className": "basis-0 box-border content-stretch flex flex-row gap-px grow items-start justify-center min-h-px min-w-px overflow-clip p-0 relative shrink-0 w-full", "children": [{"name": "LeftPanel", "id": "node-2197_1868", "type": "div", "className": "basis-0 bg-neutral-900 grow h-full min-h-px min-w-px relative shrink-0", "children": [{"name": "Block", "id": "node-2197_1869", "type": "div", "className": "relative shrink-0 w-[800px]", "children": [{"name": "Container", "id": "node-2197_5498", "type": "div", "className": "basis-0 grow min-h-px min-w-px relative rounded-[18px] shrink-0", "children": [{"name": "<PERSON><PERSON><PERSON>", "id": "node-2197_2772", "type": "div", "className": "bg-neutral-950 relative rounded-2xl shrink-0 w-full", "state": "focused", "borderColor": "#0ea5e9", "children": [{"name": "TextContainer", "id": "node-I2197_2772-2197_2559", "type": "div", "className": "box-border content-stretch flex flex-row items-center justify-start p-0 relative shrink-0 w-full", "children": [{"name": "PlaceholderText", "id": "node-I2197_2772-2197_2560", "type": "p", "text": "Tell me what you want to do", "className": "font-['Inter:Regular',_sans-serif] font-normal leading-[0] not-italic relative shrink-0 text-[16px] text-left text-neutral-600 text-nowrap"}, {"name": "<PERSON><PERSON>", "id": "node-I2197_2772-2197_2561", "type": "div", "className": "absolute h-4 left-0 top-1.5 w-px"}]}, {"name": "ButtonContainer", "id": "node-I2197_2772-2197_2562", "type": "div", "className": "box-border content-stretch flex flex-row gap-2 items-start justify-start p-0 relative shrink-0 w-full", "children": [{"name": "Button1", "id": "node-I2197_2772-2197_2563", "type": "button", "className": "relative rounded-lg shrink-0", "borderColor": "#404040", "icon": "http://localhost:3845/assets/63d2758c3528c6d24837045b6eed2943bf66e4c1.svg"}, {"name": "Button2", "id": "node-I2197_2772-2197_2564", "type": "button", "className": "relative rounded-lg shrink-0", "borderColor": "#262626", "icon": "http://localhost:3845/assets/bfbdd0e959226cb3822272b9f0a89dac5cafa174.svg"}, {"name": "InputContainer", "id": "node-I2197_2772-2197_2565", "type": "div", "className": "basis-0 grow min-h-px min-w-px self-stretch shrink-0"}, {"name": "SubmitButton", "id": "node-I2197_2772-2197_2566", "type": "button", "className": "bg-neutral-800 relative rounded-lg shrink-0", "icon": "http://localhost:3845/assets/f7770af388dcfe52d3a0c7efd5afc831d3375534.svg"}]}]}]}]}]}]}]}]}, "styles": {"colors": {"windowBackground": "#262626", "panelBackground": "#171717", "blockBackground": "#0a0a0a", "threadBackground": "#0a0a0a", "buttonBackground": "#0a0a0a", "primaryButtonBackground": "#082f49", "textColor": "#fafafa", "subtleTextColor": "#525252", "placeholderTextColor": "#525252", "caretColor": "#fafafa", "borderColor": "#404040", "focusBorderColor": "#0ea5e9", "disabledBorderColor": "#262626", "separatorColor": "#404040"}, "dimensions": {"windowRadius": "10px", "controlsHeight": "40px", "controlButtonsWidth": "80px", "searchFieldWidth": "320px", "buttonHeight": "24px", "threadRadius": "16px", "containerRadius": "18px", "iconSize": "16px", "caretWidth": "1px", "caretHeight": "16px"}, "spacing": {"windowPadding": "0px", "controlsPadding": "8px", "threadPadding": "16px", "buttonPadding": "8px", "elementGap": "8px", "buttonGap": "8px", "componentGap": "4px"}, "typography": {"labelFont": {"family": "Inter", "style": "Medium", "size": "14px", "weight": 500, "lineHeight": "18px"}, "bodyFont": {"family": "Inter", "style": "Regular", "size": "16px", "weight": 400, "lineHeight": 1.75}, "searchFont": {"family": "Inter", "style": "Regular", "size": "12px", "weight": 400, "lineHeight": 1.5}}}, "designTokens": {"Text/text-regular": "#fafafa", "UI/label": "Font(family: \"Inter\", style: Medium, size: 14, weight: 500, lineHeight: 18)", "Fill/fill-regular-subtle": "#a3a3a3", "Border/border-line": "#262626", "Text/text-regular-subtlest": "#525252", "UI/body": "Font(family: \"Inter\", style: Regular, size: 12, weight: 400, lineHeight: 1.5)", "Background/bg-button": "#0a0a0a", "Fill/fill-inverse-subtle": "#ffffff8f", "Text/text-inverse": "#ffffff", "Background/bg-primary": "#082f49", "Background/bg-window": "#262626", "Border/border-divider": "#404040", "MD/body": "Font(family: \"Inter\", style: Regular, size: 16, weight: 400, lineHeight: 1.75)", "Text/text-caret": "#fafafa", "Fill/fill-regular": "#fafafa", "Border/border-edge": "#404040", "Border/border-edge-disable": "#262626", "Fill/fill-regular-disable": "#404040", "Background/bg-disabled": "#262626", "Background/bg-block": "#0a0a0a", "Border/border-edge-focus": "#0ea5e9", "Background/bg-background": "#171717"}, "assets": {"images": [{"name": "img", "url": "http://localhost:3845/assets/f755507add3e05bbc8733477e2509b6458423722.svg", "type": "svg", "usage": "Dropdown arrow icon", "nodeId": "node-I2197_1799-2073_1057"}, {"name": "imgControlButtons", "url": "http://localhost:3845/assets/a8c1933f48906c52d8dcdebde6940ce54b9b153f.svg", "type": "svg", "usage": "Window control buttons (close, minimize, maximize)", "nodeId": "node-2197_1859"}, {"name": "imgVector12", "url": "http://localhost:3845/assets/a066c61403e8d3c3b89c3fe9ed2713330604d847.svg", "type": "svg", "usage": "Vertical separator line", "nodeId": "node-2197_1865"}, {"name": "img1", "url": "http://localhost:3845/assets/8fbd0a653349f9d584b284e27b4b4dacc349b655.svg", "type": "svg", "usage": "Search icon in text field", "nodeId": "node-I2197_1866-2078_500-67_131"}, {"name": "img2", "url": "http://localhost:3845/assets/77a2e8525b950f47935a890a3d0b0c634a6c8802.svg", "type": "svg", "usage": "Preview button icon", "nodeId": "node-I2255_6048-2179_873-2073_1094"}, {"name": "img3", "url": "http://localhost:3845/assets/63d2758c3528c6d24837045b6eed2943bf66e4c1.svg", "type": "svg", "usage": "Action button icon 1", "nodeId": "node-I2197_2772-2197_2563-2197_2150-2710_3989"}, {"name": "img4", "url": "http://localhost:3845/assets/bfbdd0e959226cb3822272b9f0a89dac5cafa174.svg", "type": "svg", "usage": "Action button icon 2", "nodeId": "node-I2197_2772-2197_2564-2197_2150-2073_1155"}, {"name": "img5", "url": "http://localhost:3845/assets/f7770af388dcfe52d3a0c7efd5afc831d3375534.svg", "type": "svg", "usage": "Submit button icon", "nodeId": "node-I2197_2772-2197_2566-2197_2493-2073_1168"}]}, "components": {"Dropdown": {"interface": "DropdownProps", "props": {"iconType": {"type": "React.ReactNode | null", "default": null, "optional": true}, "labelText": {"type": "string", "default": "Label", "optional": true}, "state": {"type": "\"Default\" | \"Hovered\" | \"Focused\" | \"Disabled\"", "default": "<PERSON><PERSON><PERSON>", "optional": true}, "showIcon": {"type": "\"On\" | \"Off\"", "default": "On", "optional": true}}, "variants": [{"name": "Default with no icon", "state": "<PERSON><PERSON><PERSON>", "showIcon": "Off", "nodeId": "node-2197_1796"}]}, "Window": {"nodeId": "node-2197_1857", "features": ["Window controls", "Title bar with dropdown", "Search field", "Action buttons", "Focused input area"]}}, "interactions": {"windowControls": {"actions": ["close", "minimize", "maximize"], "functionality": "Standard window management"}, "dropdown": {"labelText": "Untitled", "showIcon": false, "states": ["<PERSON><PERSON><PERSON>", "Hovered", "Focused", "Disabled"]}, "searchField": {"placeholder": "Search", "icon": "search", "functionality": "Content search"}, "actionButtons": {"vsCodeButton": {"text": "Open with VS Code", "action": "open_vscode"}, "previewButton": {"text": "Preview", "icon": "preview", "primary": true, "action": "preview_content"}}, "inputArea": {"state": "focused", "placeholder": "Tell me what you want to do", "focusBorder": "#0ea5e9", "features": ["Text input with caret", "Action buttons", "Submit button"]}}, "states": {"focus": {"threadBorder": "#0ea5e9", "caretVisible": true, "placeholder": "Tell me what you want to do"}, "default": {"threadBorder": "transparent", "caretVisible": false}}, "layout": {"windowLayout": {"type": "flex-column", "shadow": "0px 25px 50px 0px rgba(0,0,0,0.5), 0px 0px 0px 1px #404040", "borderRadius": "10px"}, "controlsLayout": {"type": "flex-row", "height": "40px", "sections": ["controls", "titlebar", "actions"]}, "contentLayout": {"type": "flex-row", "grow": true, "sections": ["leftPanel"]}, "inputLayout": {"type": "flex-column", "width": "800px", "centerAligned": true, "focusState": "active"}}, "technical": {"framework": "React", "language": "TypeScript", "styling": "Tailwind CSS", "alternativeCSS": "Vanilla CSS conversion needed if Tailwind not available", "dependencies": "None (avoid adding new dependencies)", "accessibility": {"considerations": ["keyboard navigation", "screen reader support", "color contrast", "focus management", "button accessibility"], "requirements": ["proper alt texts for icons", "semantic HTML structure", "ARIA labels for buttons", "focus indicators"]}}, "metadata": {"extractedFrom": "Figma Dev Mode", "extractionDate": "2025-06-25", "pageName": "Input on Focus State", "nodeIds": ["node-2197_1857", "node-2197_1858", "node-2197_1859", "node-2197_1863", "node-2197_1864", "node-2197_1865", "node-2197_1866", "node-2255_6046", "node-2255_6047", "node-2255_6048", "node-2197_1867", "node-2197_1868", "node-2197_1869", "node-2197_5498", "node-2197_2772", "node-I2197_2772-2197_2559", "node-I2197_2772-2197_2560", "node-I2197_2772-2197_2561", "node-I2197_2772-2197_2562", "node-I2197_2772-2197_2563", "node-I2197_2772-2197_2564", "node-I2197_2772-2197_2565", "node-I2197_2772-2197_2566"], "codeConnectAvailable": false, "reason": "Code Connect is only available on Organization and Enterprise plans"}}