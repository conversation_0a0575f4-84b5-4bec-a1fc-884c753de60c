{"component": {"name": "Window", "id": "node-2197_3489", "type": "Application Window", "description": "应用程序窗口处于提交状态，显示加载动画和禁用的UI元素，表示正在处理用户请求"}, "structure": {"hierarchy": [{"name": "Window", "id": "node-2197_3489", "type": "div", "className": "bg-neutral-800 box-border content-stretch flex flex-col gap-px items-start justify-start overflow-clip p-0 relative rounded-[10px] shadow-[0px_25px_50px_0px_rgba(0,0,0,0.5),0px_0px_0px_1px_#404040] size-full", "children": [{"name": "WindowControls", "id": "node-2197_3490", "type": "div", "className": "bg-neutral-800 h-10 relative rounded-tl-[10px] rounded-tr-[10px] shrink-0 w-full", "children": [{"name": "ControlButtons", "id": "node-2197_3491", "type": "img", "className": "h-10 relative shrink-0 w-20", "src": "http://localhost:3845/assets/a8c1933f48906c52d8dcdebde6940ce54b9b153f.svg"}, {"name": "TopBar", "id": "node-2197_3495", "type": "div", "className": "basis-0 grow h-full min-h-px min-w-px relative shrink-0", "children": [{"name": "Dropdown", "id": "node-2197_3496", "type": "Dropdown", "className": "h-6 relative rounded-lg shrink-0", "props": {"labelText": "Untitled", "showIcon": "Off"}}, {"name": "Separator", "id": "node-2197_3497", "type": "img", "className": "h-3 relative shrink-0 w-0", "src": "http://localhost:3845/assets/a066c61403e8d3c3b89c3fe9ed2713330604d847.svg"}, {"name": "TextField", "id": "node-2197_3498", "type": "div", "className": "h-6 relative rounded-lg shrink-0 w-80", "children": [{"name": "SearchIcon", "id": "node-I2197_3498-2078_500", "type": "img", "src": "http://localhost:3845/assets/8fbd0a653349f9d584b284e27b4b4dacc349b655.svg"}, {"name": "SearchText", "id": "node-I2197_3498-2078_501", "type": "p", "text": "Search", "className": "font-['Inter:Regular',_sans-serif] font-normal leading-[0] not-italic relative shrink-0 text-[12px] text-left text-neutral-600 text-nowrap"}]}]}, {"name": "ActionButtons", "id": "node-2255_6116", "type": "div", "children": [{"name": "VSCodeButton", "id": "node-2255_6117", "type": "button", "className": "bg-neutral-950 h-6 relative rounded-lg shrink-0", "text": "Open with VS Code"}, {"name": "PreviewButton", "id": "node-2255_6118", "type": "button", "className": "bg-sky-950 h-6 relative rounded-lg shrink-0", "text": "Preview", "icon": "http://localhost:3845/assets/77a2e8525b950f47935a890a3d0b0c634a6c8802.svg"}]}]}, {"name": "MainContent", "id": "node-2197_3499", "type": "div", "className": "basis-0 box-border content-stretch flex flex-row gap-px grow items-start justify-center min-h-px min-w-px overflow-clip p-0 relative shrink-0 w-full", "children": [{"name": "LeftPanel", "id": "node-2197_3500", "type": "div", "className": "basis-0 bg-neutral-900 grow h-full min-h-px min-w-px relative shrink-0", "children": [{"name": "Block", "id": "node-2197_3501", "type": "div", "className": "relative shrink-0 w-[800px]", "children": [{"name": "Container", "id": "node-2197_5496", "type": "div", "className": "basis-0 grow min-h-px min-w-px relative rounded-[18px] shrink-0", "children": [{"name": "<PERSON><PERSON><PERSON>", "id": "node-2197_3502", "type": "div", "className": "bg-neutral-950 relative rounded-2xl shrink-0 w-full", "state": "submitting", "borderColor": "#0ea5e9", "children": [{"name": "TextContainer", "id": "node-I2197_3502-2197_3122", "type": "div", "className": "box-border content-stretch flex flex-row items-center justify-start p-0 relative shrink-0 w-full", "children": [{"name": "SubmittedText", "id": "node-I2197_3502-2197_3123", "type": "p", "text": "Todo App with SQLite & Passkey", "className": "font-['Inter:Regular',_sans-serif] font-normal leading-[0] not-italic relative shrink-0 text-[16px] text-left text-neutral-700 text-nowrap", "textColor": "#404040", "state": "disabled"}]}, {"name": "ButtonContainer", "id": "node-I2197_3502-2197_3125", "type": "div", "className": "box-border content-stretch flex flex-row gap-2 items-start justify-start p-0 relative shrink-0 w-full", "children": [{"name": "ActionButton1", "id": "node-I2197_3502-2197_3126", "type": "button", "className": "opacity-30 relative rounded-lg shrink-0", "borderColor": "#404040", "state": "disabled", "opacity": "0.3", "icon": "http://localhost:3845/assets/63d2758c3528c6d24837045b6eed2943bf66e4c1.svg"}, {"name": "ActionButton2", "id": "node-I2197_3502-2197_3127", "type": "button", "className": "opacity-30 relative rounded-lg shrink-0", "borderColor": "#404040", "state": "disabled", "opacity": "0.3", "icon": "http://localhost:3845/assets/bfbdd0e959226cb3822272b9f0a89dac5cafa174.svg"}, {"name": "InputContainer", "id": "node-I2197_3502-2197_3128", "type": "div", "className": "basis-0 grow min-h-px min-w-px self-stretch shrink-0"}, {"name": "LoadingButton", "id": "node-I2197_3502-2197_3129", "type": "button", "className": "bg-neutral-800 relative rounded-lg shrink-0", "state": "loading", "background": "#262626", "children": [{"name": "LoadingAnimation", "id": "node-I2197_3502-2197_3129-2202_6795", "type": "div", "className": "overflow-clip relative shrink-0 size-4", "rotation": "90deg", "animation": "loading_spin", "icon": "http://localhost:3845/assets/5f89302f7334c2b89bb5c808f2fd9b33f2ef76d6.svg"}]}]}]}]}]}]}]}]}]}, "styles": {"colors": {"windowBackground": "#262626", "panelBackground": "#171717", "blockBackground": "#0a0a0a", "threadBackground": "#0a0a0a", "buttonBackground": "#0a0a0a", "primaryButtonBackground": "#082f49", "loadingButtonBackground": "#262626", "textColor": "#fafafa", "disabledTextColor": "#404040", "subtleTextColor": "#525252", "borderColor": "#404040", "focusBorderColor": "#0ea5e9", "separatorColor": "#404040"}, "dimensions": {"windowRadius": "10px", "controlsHeight": "40px", "controlButtonsWidth": "80px", "searchFieldWidth": "320px", "buttonHeight": "24px", "threadRadius": "16px", "containerRadius": "18px", "iconSize": "16px", "loadingIconSize": "12px"}, "spacing": {"windowPadding": "0px", "controlsPadding": "8px", "threadPadding": "16px", "buttonPadding": "8px", "elementGap": "8px", "buttonGap": "8px", "componentGap": "4px"}, "effects": {"disabledOpacity": "0.3", "loadingRotation": "90deg", "focusBorder": "1px solid #0ea5e9"}, "typography": {"labelFont": {"family": "Inter", "style": "Medium", "size": "14px", "weight": 500, "lineHeight": "18px"}, "bodyFont": {"family": "Inter", "style": "Regular", "size": "16px", "weight": 400, "lineHeight": 1.75}, "searchFont": {"family": "Inter", "style": "Regular", "size": "12px", "weight": 400, "lineHeight": 1.5}}}, "designTokens": {"Text/text-regular": "#fafafa", "UI/label": "Font(family: \"Inter\", style: Medium, size: 14, weight: 500, lineHeight: 18)", "Fill/fill-regular-subtle": "#a3a3a3", "Border/border-line": "#262626", "Text/text-regular-subtlest": "#525252", "UI/body": "Font(family: \"Inter\", style: Regular, size: 12, weight: 400, lineHeight: 1.5)", "Background/bg-button": "#0a0a0a", "Fill/fill-inverse-subtle": "#ffffff8f", "Text/text-inverse": "#ffffff", "Background/bg-primary": "#082f49", "Background/bg-window": "#262626", "Border/border-divider": "#404040", "Text/text-regular-disable": "#404040", "MD/body": "Font(family: \"Inter\", style: Regular, size: 16, weight: 400, lineHeight: 1.75)", "Fill/fill-regular": "#fafafa", "Border/border-edge": "#404040", "Background/bg-disabled": "#262626", "Background/bg-block": "#0a0a0a", "Border/border-edge-focus": "#0ea5e9", "Background/bg-background": "#171717"}, "assets": {"images": [{"name": "img", "url": "http://localhost:3845/assets/f755507add3e05bbc8733477e2509b6458423722.svg", "type": "svg", "usage": "Dropdown arrow icon", "nodeId": "node-I2197_1799-2073_1057"}, {"name": "imgControlButtons", "url": "http://localhost:3845/assets/a8c1933f48906c52d8dcdebde6940ce54b9b153f.svg", "type": "svg", "usage": "Window control buttons (close, minimize, maximize)", "nodeId": "node-2197_3491"}, {"name": "imgVector12", "url": "http://localhost:3845/assets/a066c61403e8d3c3b89c3fe9ed2713330604d847.svg", "type": "svg", "usage": "Vertical separator line", "nodeId": "node-2197_3497"}, {"name": "img1", "url": "http://localhost:3845/assets/8fbd0a653349f9d584b284e27b4b4dacc349b655.svg", "type": "svg", "usage": "Search icon in text field", "nodeId": "node-I2197_3498-2078_500-67_131"}, {"name": "img2", "url": "http://localhost:3845/assets/77a2e8525b950f47935a890a3d0b0c634a6c8802.svg", "type": "svg", "usage": "Preview button icon", "nodeId": "node-I2255_6118-2179_873-2073_1094"}, {"name": "img3", "url": "http://localhost:3845/assets/63d2758c3528c6d24837045b6eed2943bf66e4c1.svg", "type": "svg", "usage": "Action button icon 1 (disabled)", "nodeId": "node-I2197_3502-2197_3126-2197_2150-2710_3989"}, {"name": "img4", "url": "http://localhost:3845/assets/bfbdd0e959226cb3822272b9f0a89dac5cafa174.svg", "type": "svg", "usage": "Action button icon 2 (disabled)", "nodeId": "node-I2197_3502-2197_3127-2197_2150-2073_1155"}, {"name": "img5", "url": "http://localhost:3845/assets/5f89302f7334c2b89bb5c808f2fd9b33f2ef76d6.svg", "type": "svg", "usage": "Loading spinner icon", "nodeId": "node-I2197_3502-2197_3129-2202_6795-2202_6786-2073_1133"}]}, "components": {"Dropdown": {"interface": "DropdownProps", "props": {"iconType": {"type": "React.ReactNode | null", "default": null, "optional": true}, "labelText": {"type": "string", "default": "Label", "optional": true}, "state": {"type": "\"Default\" | \"Hovered\" | \"Focused\" | \"Disabled\"", "default": "<PERSON><PERSON><PERSON>", "optional": true}, "showIcon": {"type": "\"On\" | \"Off\"", "default": "On", "optional": true}}, "variants": [{"name": "Default with no icon", "state": "<PERSON><PERSON><PERSON>", "showIcon": "Off", "nodeId": "node-2197_1796"}]}, "Window": {"nodeId": "node-2197_3489", "features": ["Window controls", "Title bar with dropdown", "Search field", "Action buttons", "Submitting input area", "Loading animation"]}, "LoadingAnimation": {"nodeId": "node-I2197_3502-2197_3129-2202_6795", "type": "Loading Spinner", "rotation": "90deg", "animationType": "continuous_rotation", "size": "16px"}}, "interactions": {"windowControls": {"actions": ["close", "minimize", "maximize"], "functionality": "Standard window management"}, "dropdown": {"labelText": "Untitled", "showIcon": false, "states": ["<PERSON><PERSON><PERSON>", "Hovered", "Focused", "Disabled"]}, "searchField": {"placeholder": "Search", "icon": "search", "functionality": "Content search"}, "actionButtons": {"vsCodeButton": {"text": "Open with VS Code", "action": "open_vscode"}, "previewButton": {"text": "Preview", "icon": "preview", "primary": true, "action": "preview_content"}}, "inputArea": {"state": "submitting", "submittedText": "Todo App with SQLite & Passkey", "focusBorder": "#0ea5e9", "features": ["Text grayed out (disabled)", "Action buttons disabled with opacity", "Submit button shows loading spinner", "All interactions disabled during submission"]}}, "states": {"submitting": {"threadBorder": "#0ea5e9", "textColor": "#404040", "textState": "disabled", "actionButtonsOpacity": "0.3", "actionButtonsDisabled": true, "submitButtonState": "loading", "submitButtonBackground": "#262626", "loadingAnimation": {"visible": true, "rotation": "90deg", "type": "spinner"}}, "loadingButton": {"background": "#262626", "animation": "loading_spinner", "iconRotation": "90deg", "disabled": true}}, "animations": {"loadingSpinner": {"type": "rotation", "duration": "continuous", "direction": "clockwise", "initialRotation": "90deg", "element": "loading icon"}}, "transitions": {"fromPage3": {"textColor": "#fafafa → #404040", "submitButton": "gradient → #262626 with loading", "actionButtons": "normal → 30% opacity", "caret": "visible → hidden"}, "toPage5": {"description": "Loading completes, response appears", "expectedChanges": ["Loading spinner disappears", "Response content appears", "UI becomes interactive again"]}}, "layout": {"windowLayout": {"type": "flex-column", "shadow": "0px 25px 50px 0px rgba(0,0,0,0.5), 0px 0px 0px 1px #404040", "borderRadius": "10px"}, "controlsLayout": {"type": "flex-row", "height": "40px", "sections": ["controls", "titlebar", "actions"]}, "contentLayout": {"type": "flex-row", "grow": true, "sections": ["leftPanel"]}, "inputLayout": {"type": "flex-column", "width": "800px", "centerAligned": true, "focusState": "submitting"}}, "technical": {"framework": "React", "language": "TypeScript", "styling": "Tailwind CSS", "alternativeCSS": "Vanilla CSS conversion needed if Tailwind not available", "dependencies": "None (avoid adding new dependencies)", "accessibility": {"considerations": ["keyboard navigation disabled during submission", "screen reader announcements for loading state", "color contrast maintained", "loading state clearly indicated", "button accessibility during disabled state"], "requirements": ["proper alt texts for loading icons", "ARIA labels for loading state", "semantic HTML structure", "loading announcements", "disabled state indicators"]}, "animations": {"loadingSpinner": {"implementation": "CSS animation or React transition", "duration": "continuous", "easing": "linear"}}}, "metadata": {"extractedFrom": "Figma Dev Mode", "extractionDate": "2025-06-25", "pageName": "Submitting State (Transition)", "transitionType": "Page 3 → Page 5", "keyFeatures": ["Loading animation with spinner", "Disabled UI elements with opacity", "Text color changed to disabled state", "Submit button shows loading state", "Focus border remains active"], "keyDifferences": ["Text color: #fafafa → #404040 (disabled)", "Action buttons: opacity set to 0.3", "Submit button: gradient → #262626 with loading spinner", "New loading animation component", "Caret removed (no longer visible)"], "nodeIds": ["node-2197_3489", "node-2197_3490", "node-2197_3491", "node-2197_3495", "node-2197_3496", "node-2197_3497", "node-2197_3498", "node-2255_6116", "node-2255_6117", "node-2255_6118", "node-2197_3499", "node-2197_3500", "node-2197_3501", "node-2197_5496", "node-2197_3502", "node-I2197_3502-2197_3122", "node-I2197_3502-2197_3123", "node-I2197_3502-2197_3125", "node-I2197_3502-2197_3126", "node-I2197_3502-2197_3127", "node-I2197_3502-2197_3128", "node-I2197_3502-2197_3129", "node-I2197_3502-2197_3129-2202_6795"], "codeConnectAvailable": false, "reason": "Code Connect is only available on Organization and Enterprise plans"}}