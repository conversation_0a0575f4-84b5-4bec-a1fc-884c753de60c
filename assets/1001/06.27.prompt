# 完整项目复刻 Prompt

## 项目概述
创建一个基于 Figma 设计的 AI 代码生成界面，这是一个类似 Notion 的单页应用，具有完整的 AI 交互流程和状态管理。

## 技术栈要求
- **框架**: Next.js 14 + React + TypeScript
- **样式**: Tailwind CSS (必须配置 Inter 字体)
- **状态管理**: 基于状态机的 AI 交互流程
- **字体**: Inter (Google Fonts) - 必须正确加载和配置
- **图标**: 内联 SVG (禁止使用远程图片 URL)

## 产品素材
src/assets/1001/
├── prd-1001.md （产品讨论记录）
├── mem.log （其他的日志文件）
├── page-1-before-input.json （figma 设计稿的结构文件）
├── page-2-input-on-focus.json （figma 设计稿的结构文件）
├── page-3-prompt-entered.json （figma 设计稿的结构文件）
├── page-4-submitting-state.json （figma 设计稿的结构文件）
├── page-5-generating.json （figma 设计稿的结构文件）
├── page-6-generating-with-ai-question.json （figma 设计稿的结构文件）
├── page-7-thread-continuation.json （figma 设计稿的结构文件）
├── page-8-thread-input-filled.json （figma 设计稿的结构文件）
├── page-9-prompt-sending-process.json （figma 设计稿的结构文件）
├── page-10-conversation-collapsed.json （figma 设计稿的结构文件）
├── page-11-ai-generating-structured-content.json （figma 设计稿的结构文件）
└── page-12-generation-completed.json （figma 设计稿的结构文件）

## 关键架构要求

### 1. 项目结构
```
src/
├── app/
│   ├── layout.tsx (Inter 字体配置)
│   ├── page.tsx (首页，文档列表)
│   ├── docs/[gid]/page.tsx (动态文档页面)
│   └── globals.css (Inter 字体样式)
├── components/
│   ├── figma/
│   │   ├── Window/LayoutWindow.tsx (主布局)
│   │   ├── Navigation/TopNavigation.tsx (顶部工具栏)
│   │   ├── Sidebar/NavigationSidebar.tsx (左侧覆盖层)
│   │   ├── ContentArea/MainContentArea.tsx (核心交互区域)
│   │   └── Controls/WindowControls.tsx (窗口控制按钮)
│   └── ui/LoadingSpinner.tsx
└── lib/ai-state-machine.ts (完整的 12 状态机)
```

### 2. 状态机核心
必须实现包含 12 个状态的完整 AI 交互流程：
```typescript
type AIState = 
  | 'before-input'           // 初始状态
  | 'input-on-focus'         // 输入框聚焦
  | 'prompt-entered'         // 用户输入完成
  | 'submitting-state'       // 提交中
  | 'generating'             // AI 生成中
  | 'generating-with-ai-question' // AI 询问状态
  | 'thread-continuation'    // 多轮对话
  | 'thread-input-filled'    // 多轮输入
  | 'prompt-sending-process' // 多轮提交
  | 'conversation-collapsed' // 对话折叠
  | 'ai-generating-structured-content' // 结构化生成
  | 'generation-completed'   // 生成完成
```

## 设计规范

### 1. 布局结构
- **不是桌面窗口应用**: 页面布局，窗口装饰由外部 TUI 提供
- **顶部工具栏**: "Untitled" 下拉菜单 + 搜索框 + "Open with VS Code" + "Preview" 按钮
- **主内容区**: 800px 居中宽度，深色背景 (#171717)
- **左侧覆盖层**: 296px 宽度，仅在生成状态显示，绝对定位

### 2. 颜色系统
```css
--color-page-bg: #171717;      /* 页面背景 */
--color-sidebar-bg: #262626;   /* 侧边栏背景 */
--color-panel-bg: #0a0a0a;     /* 面板背景 */
--color-text-primary: #fafafa;  /* 主要文本 */
--color-text-secondary: #a3a3a3; /* 次要文本 */
--color-brand: #0ea5e9;        /* 品牌色/焦点边框 */
--color-neutral-600: #525252;  /* 禁用文本 */
--color-neutral-800: #262626;  /* 组件背景 */
--color-neutral-950: #0a0a0a;  /* 深色背景 */
```

### 3. 字体配置要求
**必须严格按照以下配置**：

#### layout.tsx 中的 Inter 字体设置
```typescript
const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
  weight: ['400', '500', '600'],
  style: ['normal'],
});
```

#### globals.css 中的字体样式
```css
:root {
  --font-inter: 'Inter', sans-serif;
}

body {
  font-family: var(--font-inter);
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.font-inter {
  font-family: var(--font-inter);
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}
```

#### Tailwind 配置中的字体
```typescript
fontFamily: {
  'inter': ['Inter', 'sans-serif'],
},
```

### 4. 字体使用规范
- **禁止使用** `font-['Inter']` 语法
- **必须使用** `font-inter` Tailwind 类
- **行高设置**: 
  - 14px 字体使用 `leading-[18px]`
  - 16px 字体使用 `leading-[28px]` 或 `leading-[20px]`

## 核心交互状态实现

### 1. 初始状态 (before-input)
```jsx
// 简单的文本提示 + 菜单触发器装饰
<p className="font-inter font-normal text-[16px] text-neutral-400">
  Write, press 'space' for AI
</p>
```

### 2. 聚焦状态 (input-on-focus)
```jsx
// 蓝色边框的输入框 + 操作按钮
<div className="border-2 border-sky-500 rounded-2xl p-4">
  <input placeholder="Tell me what you want to do" />
  {/* Plus 图标 + Attachment 图标 + Submit 按钮 */}
</div>
```

### 3. 生成状态 (generating)
```jsx
// Prompt 完成面板 (半透明背景)
<div style={{ background: 'rgba(255,255,255,0.03)' }}>
  <p>Prompt: | Worked for 1m 22s</p>
</div>

// 代码生成块 (带加载动画)
<div className="bg-neutral-950">
  {/* 旋转的 loading SVG + 渐变标题 */}
  <p style={{ background: 'linear-gradient(to right, #fafafa, #404040)' }}>
    Build a Todo App with SQLite and Passkey Authentication
  </p>
</div>
```

## 图标要求

### 严格要求：必须使用内联 SVG
**禁止使用任何远程图片 URL**，所有图标必须是内联 SVG：

#### 操作按钮图标
```jsx
// Plus 图标
<svg className="w-4 h-4 text-neutral-400" fill="currentColor" viewBox="0 0 16 16">
  <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
</svg>

// Attachment 图标
<svg className="w-4 h-4 text-neutral-400" fill="currentColor" viewBox="0 0 16 16">
  <path d="M4.5 3a2.5 2.5 0 0 1 5 0v9a1.5 1.5 0 0 1-3 0V5a.5.5 0 0 1 1 0v7a.5.5 0 0 0 1 0V3a1.5 1.5 0 1 0-3 0v9a2.5 2.5 0 0 0 5 0V5a.5.5 0 0 1 1 0v7a3.5 3.5 0 1 1-7 0V3z"/>
</svg>
```

#### 提交按钮图标
```jsx
// Play/Submit 图标
<svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 16 16">
  <path d="m12.14 8.753-5.482 4.796c-.646.566-1.658.106-1.658-.753V3.204a1 1 0 0 1 1.659-.753l5.48 4.796a1 1 0 0 1 0 1.506z"/>
</svg>
```

#### 加载动画图标
```jsx
// Loading Spinner
<svg className="w-4 h-4 animate-spin text-neutral-400" fill="none" viewBox="0 0 24 24">
  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
  <path className="opacity-75" fill="currentColor" d="m12 2 A10 10 0 0 1 22 12 h-4 A6 6 0 0 0 12 6 V2 Z"></path>
</svg>
```

## 关键错误避免清单

### 1. 字体错误
- ❌ **错误**: 使用 `font-['Inter']` 语法
- ✅ **正确**: 使用 `font-inter` Tailwind 类
- ❌ **错误**: 使用 `leading-[0]` 无效行高
- ✅ **正确**: 使用具体数值如 `leading-[18px]`

### 2. 图标错误
- ❌ **错误**: 使用远程图片 URL (localhost:3845/assets/...)
- ✅ **正确**: 使用内联 SVG 代码
- ❌ **错误**: 图标显示为方格子或失败状态
- ✅ **正确**: 清晰的 SVG 图标，带适当颜色

### 3. 布局错误
- ❌ **错误**: 实现桌面窗口装饰 (阴影、圆角、窗口控制)
- ✅ **正确**: 简单页面布局，窗口装饰由外部提供
- ❌ **错误**: 右侧固定侧边栏
- ✅ **正确**: 左侧绝对定位覆盖层，仅在生成状态显示

### 4. 交互错误
- ❌ **错误**: 显示 12 个状态切换按钮 (调试界面)
- ✅ **正确**: 根据当前状态显示对应的真实 UI
- ❌ **错误**: 静态 mock 界面
- ✅ **正确**: 可交互的输入框、按钮，状态机驱动

### 5. 热重载错误
- ❌ **错误**: 多个 dev 服务器冲突导致热重载失效
- ✅ **正确**: 确保只运行一个 `npm run dev`，检查端口冲突

## 验收标准

### 功能验收
1. **页面加载**: 显示 "Write, press 'space' for AI" 初始状态
2. **交互流程**: 点击输入 → 聚焦状态 → 输入文本 → 提交按钮激活 → 生成状态
3. **图标显示**: 所有图标都是清晰的 SVG，无失败状态
4. **字体渲染**: Inter 字体正确加载，文字清晰美观
5. **侧边栏**: 仅在生成状态显示，包含目录图标和加载动画

### 技术验收
1. **构建成功**: `npm run build` 无错误
2. **类型检查**: `npm run type-check` 无错误  
3. **热重载**: 代码修改后页面自动更新
4. **状态机**: 完整的 12 状态定义和转换逻辑
5. **响应式**: 800px 居中布局，适配不同屏幕

### 视觉验收
1. **配色**: 深色主题，蓝色 (#0ea5e9) 强调色
2. **间距**: 符合 Figma 设计规范
3. **动画**: loading 图标旋转动画，按钮 hover 效果
4. **层级**: 正确的文字层级和视觉权重

---

**使用此 Prompt 可以完整复刻当前项目状态，包括所有架构决策、设计规范和避免我们踩过的所有坑。**