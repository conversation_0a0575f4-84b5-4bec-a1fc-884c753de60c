# AI-Powered Code Generation Interface - Product Requirements Document

## 1. 产品概述

我希望做一个单页面应用，整个应用是一个类似 Notion 的AI增强编辑页面。这是一个集成了智能代码生成功能的现代化编辑器，支持多轮对话式AI交互和实时内容生成。页面本身就是一个完整的 Notion Page，专注于内容编辑和AI交互功能。

### 1.1 核心价值
- 提供自然的文本编辑体验
- 集成强大的AI代码生成能力
- 支持多轮对话和上下文保持
- 实现结构化内容的智能生成

## 2. 页面布局与设计

### 2.1 整体页面设计
- **页面类型**: 单一 Notion Page，全屏编辑界面
- **页面配色**: 深色主题，主要使用中性色调
- **布局结构**: 侧边栏 + 主编辑区域的两栏布局

### 2.2 侧边栏导航
- **位置**: 左侧固定位置，宽度296px
- **功能**: 
  - 显示当前页面的内容结构
  - 实时反映正在生成的内容层次
  - 支持章节导航和状态指示
- **状态指示**:
  - 加载中: 旋转的加载图标
  - 已完成: 成功完成图标
  - 当前激活: 蓝色高亮显示 (#0ea5e9)

### 2.3 主编辑区域
- **定位**: 页面中央，类似 Notion 编辑器的内容区域
- **宽度**: 自适应，最大宽度约800px
- **功能**: 承载所有编辑和AI交互功能

## 3. 编辑功能描述

### 3.1 基础编辑功能
- **文本格式**: 支持加粗、高亮等简单格式
- **编辑模式**: 支持预览模式和编辑模式切换，预览模式下不能修改
- **输入支持**: 可以打字、粘贴文本内容

### 3.2 编辑区域操作
- **AI唤起**: 使用空格键唤起AI输入框
- **内容分割**: AI输入框产生的内容会把编辑区域分成两部分
  - AI生成前的原有内容
  - AI生成的新内容
- **内容排列**: 两部分内容分别放在独立的区域中

### 3.3 编辑区域元素
- **文本内容**: 支持多种字体大小和样式
- **列表**: 支持有序和无序列表
- **表情符号**: 支持emoji表情符号插入
- **代码块**: 
  - 语法高亮显示
  - 支持多种编程语言
  - 显示行号
  - 语言选择器
  - 复制按钮
  - 运行按钮（点击后显示等待状态）

## 4. AI交互系统

### 4.1 AI输入框设计
基于我们讨论的12页完整交互流程，AI输入框包含以下状态：

#### 4.1.1 初始状态 (页面1-2)
- **输入框**: 默认状态，等待用户输入
- **焦点状态**: 点击后显示蓝色边框 (#0ea5e9)
- **提交按钮**: 初始为灰色不可用状态 (#262626)

**设计稿数据包**:
- `page-1-before-input.json` - AI输入框初始状态，显示默认的空白输入框界面
- `page-2-input-on-focus.json` - 输入框获得焦点状态，显示蓝色边框和光标

#### 4.1.2 输入状态 (页面3)
- **文本输入**: 用户输入Prompt内容
- **按钮激活**: 输入内容后，提交按钮变为蓝色渐变可用状态
- **光标显示**: 在输入末尾显示文本光标

**设计稿数据包**:
- `page-3-prompt-entered.json` - 用户输入文本后的状态，显示填充的输入框和激活的提交按钮

#### 4.1.3 提交状态 (页面4)
- **加载动画**: 提交按钮显示旋转的加载图标
- **UI禁用**: 所有交互元素变为禁用状态，透明度30%
- **文本变灰**: 输入文本颜色变为禁用色 (#404040)
- **边框保持**: 蓝色焦点边框保持显示

**设计稿数据包**:
- `page-4-submitting-state.json` - 提交状态界面，显示加载动画和禁用的UI元素

### 4.2 AI生成过程

#### 4.2.1 响应生成阶段 (页面5)
- **Prompt面板**: 显示已提交的Prompt和执行时间
- **实时生成**: AI开始生成响应内容
- **加载指示**: 多个位置显示加载动画
  - 内容区域头部加载图标
  - 侧边栏加载图标
- **渐变效果**: 生成的标题使用渐变文字效果

**设计稿数据包**:
- `page-5-generating.json` - AI响应生成阶段，显示Prompt面板和生成过程中的加载状态

#### 4.2.2 AI交互询问 (页面6)
- **问题区域**: 蓝色高亮显示AI的问题 (#082f49)
- **操作选项**: 提供三个交互按钮
  - Accept: 接受当前生成内容
  - Thread: 继续对话
  - Re-run: 重新生成

**设计稿数据包**:
- `page-6-generating-with-ai-question.json` - AI生成过程中的交互询问界面，显示问题区域和三个操作按钮

#### 4.2.3 多轮对话 (页面7-9)
- **Thread模式**: 支持多轮对话功能
- **上下文保持**: 维护完整的对话历史
- **输入重用**: 新的输入框复用相同的交互逻辑
- **状态传递**: 从Thread选择到新输入的状态过渡

**设计稿数据包**:
- `page-7-thread-continuation.json` - Thread模式启动界面，显示多轮对话的开始状态
- `page-8-thread-input-filled.json` - Thread模式下用户输入新内容的状态
- `page-9-prompt-sending-process.json` - Thread模式下Prompt发送过程，显示完整的对话历史

### 4.3 内容展示与管理

#### 4.3.1 内容折叠功能 (页面10)
- **折叠状态**: 对话内容可以折叠显示
- **数据保持**: 折叠后数据仍然保留
- **展开交互**: 点击可展开查看完整内容
- **简洁界面**: 折叠后提供清晰简洁的界面

**设计稿数据包**:
- `page-10-conversation-collapsed.json` - 对话折叠后的界面，显示简洁的内容概览和展开交互提示

#### 4.3.2 结构化内容生成 (页面11-12)
- **内容类型**:
  - 主标题 (h1, 32px): Inter Semi Bold
  - 小标题 (h2, 28px): Inter Semi Bold  
  - 正文列表 (16px): Inter Regular
  - 代码块 (14px): Menlo Regular
- **emoji集成**: 支持在标题中使用emoji图标
- **语法高亮**: 代码块支持完整的语法高亮
- **目录结构**: 生成项目目录结构展示

**设计稿数据包**:
- `page-11-ai-generating-structured-content.json` - AI生成结构化内容过程，显示标题、列表和代码块的生成状态
- `page-12-generation-completed.json` - 内容生成完成状态，显示完整的结构化内容和项目目录

## 5. 设计系统

### 5.1 颜色规范
```
- 页面背景: #171717
- 侧边栏背景: #262626
- 块背景: #0a0a0a
- 主要文本: #fafafa
- 次要文本: #a3a3a3
- 禁用文本: #404040
- 品牌色: #0ea5e9
- 主按钮: #082f49
- 边框色: #404040
- 成功色: #4ade80
```

### 5.2 字体规范
```
- UI/label: Inter Medium, 14px, weight 500, lineHeight 18px
- UI/body: Inter Regular, 12px, weight 400, lineHeight 1.5
- MD/h1: Inter Semi Bold, 32px, weight 600, lineHeight 1.2
- MD/h2: Inter Semi Bold, 28px, weight 600, lineHeight 1.2  
- MD/body: Inter Regular, 16px, weight 400, lineHeight 1.75
- MD/code-block: Menlo Regular, 14px, weight 400, lineHeight 18px
```

### 5.3 间距规范
```
- 页面内边距: 16px
- 面板内边距: 16px
- 元素间距: 24px
- 按钮间距: 8px
- 组件间距: 4px
- 侧边栏内边距: 32px 0px 32px 40px
```

## 6. 交互状态定义

### 6.1 按钮状态
- **非活跃状态**: 灰色背景 (#262626)，不可交互
- **活跃状态**: 蓝色渐变背景，可以点击
- **加载状态**: 显示旋转加载图标，禁用交互
- **禁用状态**: 30%透明度，不响应交互

### 6.2 输入框状态
- **默认状态**: 普通边框，等待输入
- **焦点状态**: 蓝色边框 (#0ea5e9)，显示光标
- **填充状态**: 包含用户输入文本
- **提交状态**: 文本变灰，边框保持，禁用输入

### 6.3 加载动画
- **类型**: 旋转加载图标
- **初始角度**: 90度
- **动画方向**: 顺时针连续旋转
- **使用位置**: 侧边栏、内容区域、提交按钮

## 7. 技术要求

### 7.1 框架选择
- **前端框架**: React with TypeScript
- **样式方案**: Tailwind CSS (提供vanilla CSS备选方案)
- **状态管理**: 组件级状态管理
- **动画实现**: CSS animations or React transitions

### 7.2 依赖管理
- **原则**: 避免添加新的外部依赖
- **现有工具**: 优先使用项目中已有的库和工具
- **兼容性**: 确保跨浏览器兼容性

### 7.3 无障碍访问
- **语义化HTML**: 使用正确的标题层次 (h1, h2)
- **键盘导航**: 支持完整的键盘操作
- **屏幕阅读器**: 为动态内容提供适当的ARIA标签
- **状态提示**: 为加载和状态变化提供明确提示
- **颜色对比**: 确保足够的颜色对比度

## 8. 实现优先级

### 8.1 第一阶段 - 基础编辑器
1. 基本的页面布局和侧边栏
2. 文本编辑功能和基础格式
3. 侧边栏导航结构
4. 基础的设计系统实现

### 8.2 第二阶段 - AI集成
1. AI输入框的完整状态机
2. 加载动画和状态指示
3. Prompt提交和响应处理
4. 基础的内容生成显示

### 8.3 第三阶段 - 高级功能
1. 多轮对话和Thread功能
2. 内容折叠和展开
3. 结构化内容生成
4. 代码语法高亮

### 8.4 第四阶段 - 优化完善
1. 性能优化和动画细化
2. 无障碍访问完善
3. 跨浏览器兼容性
4. 错误处理和边界情况

## 9. 验收标准

### 9.1 功能验收
- [ ] 完整的12页交互流程无缝运行
- [ ] 所有按钮状态正确响应
- [ ] 加载动画在正确的时机显示和隐藏
- [ ] 多轮对话功能正常工作
- [ ] 内容折叠和展开功能正常

### 9.2 设计验收
- [ ] 所有颜色、字体、间距符合设计规范
- [ ] 动画效果流畅自然
- [ ] 页面布局适配不同屏幕尺寸
- [ ] 深色主题实现完整

### 9.3 技术验收
- [ ] 代码质量符合团队标准
- [ ] TypeScript类型定义完整
- [ ] 无障碍访问标准达标
- [ ] 性能指标满足要求

## 10. 设计稿数据包总览

### 10.1 完整交互流程设计稿
以下12个JSON文件包含了完整AI交互流程的详细设计数据：

| 页面编号 | 文件名 | 功能描述 | 关键状态 |
|---------|-------|---------|---------|
| 页面1 | `page-1-before-input.json` | AI输入框初始状态 | 空白输入框，等待输入 |
| 页面2 | `page-2-input-on-focus.json` | 输入框焦点状态 | 蓝色边框，显示光标 |
| 页面3 | `page-3-prompt-entered.json` | 用户输入完成状态 | 填充文本，按钮激活 |
| 页面4 | `page-4-submitting-state.json` | 提交加载状态 | 加载动画，UI禁用 |
| 页面5 | `page-5-generating.json` | AI响应生成阶段 | Prompt面板，生成指示 |
| 页面6 | `page-6-generating-with-ai-question.json` | AI交互询问 | 问题区域，操作按钮 |
| 页面7 | `page-7-thread-continuation.json` | Thread模式启动 | 多轮对话开始 |
| 页面8 | `page-8-thread-input-filled.json` | Thread输入状态 | 新一轮输入完成 |
| 页面9 | `page-9-prompt-sending-process.json` | Thread发送过程 | 完整对话历史 |
| 页面10 | `page-10-conversation-collapsed.json` | 内容折叠状态 | 简洁界面，可展开 |
| 页面11 | `page-11-ai-generating-structured-content.json` | 结构化内容生成 | 标题列表代码生成 |
| 页面12 | `page-12-generation-completed.json` | 生成完成状态 | 完整内容和目录 |

### 10.2 数据包使用说明
- **文件格式**: 所有设计稿均为JSON格式，包含完整的组件结构和样式信息
- **文件位置**: `/assets/1001/` 目录下
- **使用方式**: 每个JSON文件对应一个完整的页面状态，可直接用于组件开发和样式实现
- **依赖关系**: 按页面编号顺序形成完整的用户交互流程
- **更新频率**: 设计稿数据包与功能描述保持同步更新

---

*本PRD基于完整的12页交互流程设计，涵盖了从初始输入到最终内容生成的完整用户体验。每个功能点都有对应的设计稿数据包支持实现。*
