{"component": {"name": "Window", "id": "node-2197_2912", "type": "Application Window", "description": "应用程序窗口，用户已输入Prompt文本但尚未发送，提交按钮为激活状态"}, "structure": {"hierarchy": [{"name": "Window", "id": "node-2197_2912", "type": "div", "className": "bg-neutral-800 box-border content-stretch flex flex-col gap-px items-start justify-start overflow-clip p-0 relative rounded-[10px] shadow-[0px_25px_50px_0px_rgba(0,0,0,0.5),0px_0px_0px_1px_#404040] size-full", "children": [{"name": "WindowControls", "id": "node-2197_2913", "type": "div", "className": "bg-neutral-800 h-10 relative rounded-tl-[10px] rounded-tr-[10px] shrink-0 w-full", "children": [{"name": "ControlButtons", "id": "node-2197_2914", "type": "img", "className": "h-10 relative shrink-0 w-20", "src": "http://localhost:3845/assets/a8c1933f48906c52d8dcdebde6940ce54b9b153f.svg"}, {"name": "TopBar", "id": "node-2197_2918", "type": "div", "className": "basis-0 grow h-full min-h-px min-w-px relative shrink-0", "children": [{"name": "Dropdown", "id": "node-2197_2919", "type": "Dropdown", "className": "h-6 relative rounded-lg shrink-0", "props": {"labelText": "Untitled", "showIcon": "Off"}}, {"name": "Separator", "id": "node-2197_2920", "type": "img", "className": "h-3 relative shrink-0 w-0", "src": "http://localhost:3845/assets/a066c61403e8d3c3b89c3fe9ed2713330604d847.svg"}, {"name": "TextField", "id": "node-2197_2921", "type": "div", "className": "h-6 relative rounded-lg shrink-0 w-80", "children": [{"name": "SearchIcon", "id": "node-I2197_2921-2078_500", "type": "img", "src": "http://localhost:3845/assets/8fbd0a653349f9d584b284e27b4b4dacc349b655.svg"}, {"name": "SearchText", "id": "node-I2197_2921-2078_501", "type": "p", "text": "Search", "className": "font-['Inter:Regular',_sans-serif] font-normal leading-[0] not-italic relative shrink-0 text-[12px] text-left text-neutral-600 text-nowrap"}]}]}, {"name": "ActionButtons", "id": "node-2255_6088", "type": "div", "children": [{"name": "VSCodeButton", "id": "node-2255_6089", "type": "button", "className": "bg-neutral-950 h-6 relative rounded-lg shrink-0", "text": "Open with VS Code"}, {"name": "PreviewButton", "id": "node-2255_6090", "type": "button", "className": "bg-sky-950 h-6 relative rounded-lg shrink-0", "text": "Preview", "icon": "http://localhost:3845/assets/77a2e8525b950f47935a890a3d0b0c634a6c8802.svg"}]}]}, {"name": "MainContent", "id": "node-2197_2922", "type": "div", "className": "basis-0 box-border content-stretch flex flex-row gap-px grow items-start justify-center min-h-px min-w-px overflow-clip p-0 relative shrink-0 w-full", "children": [{"name": "LeftPanel", "id": "node-2197_2923", "type": "div", "className": "basis-0 bg-neutral-900 grow h-full min-h-px min-w-px relative shrink-0", "children": [{"name": "Block", "id": "node-2197_2924", "type": "div", "className": "relative shrink-0 w-[800px]", "children": [{"name": "Container", "id": "node-2197_5497", "type": "div", "className": "basis-0 grow min-h-px min-w-px relative rounded-[18px] shrink-0", "children": [{"name": "<PERSON><PERSON><PERSON>", "id": "node-2197_2925", "type": "div", "className": "bg-neutral-950 relative rounded-2xl shrink-0 w-full", "state": "focused", "borderColor": "#0ea5e9", "children": [{"name": "TextContainer", "id": "node-I2197_2925-2197_2692", "type": "div", "className": "box-border content-stretch flex flex-row items-center justify-start p-0 relative shrink-0 w-full", "children": [{"name": "UserInput", "id": "node-I2197_2925-2197_2693", "type": "p", "text": "Todo App with SQLite & Passkey", "className": "font-['Inter:Regular',_sans-serif] font-normal leading-[0] not-italic relative shrink-0 text-[16px] text-left text-neutral-50 text-nowrap", "textColor": "#fafafa"}, {"name": "<PERSON><PERSON>", "id": "node-I2197_2925-2197_2694", "type": "div", "className": "h-4 shrink-0 w-px", "state": "visible"}]}, {"name": "ButtonContainer", "id": "node-I2197_2925-2197_2695", "type": "div", "className": "box-border content-stretch flex flex-row gap-2 items-start justify-start p-0 relative shrink-0 w-full", "children": [{"name": "ActionButton1", "id": "node-I2197_2925-2197_2696", "type": "button", "className": "relative rounded-lg shrink-0", "borderColor": "#404040", "icon": "http://localhost:3845/assets/63d2758c3528c6d24837045b6eed2943bf66e4c1.svg"}, {"name": "ActionButton2", "id": "node-I2197_2925-2197_2697", "type": "button", "className": "relative rounded-lg shrink-0", "borderColor": "#404040", "icon": "http://localhost:3845/assets/bfbdd0e959226cb3822272b9f0a89dac5cafa174.svg"}, {"name": "InputContainer", "id": "node-I2197_2925-2197_2698", "type": "div", "className": "basis-0 grow min-h-px min-w-px self-stretch shrink-0"}, {"name": "SubmitButton", "id": "node-I2197_2925-2197_2699", "type": "button", "className": "bg-gradient-to-b from-[#0284c7] relative rounded-lg shrink-0 to-[#38bdf8]", "state": "active", "background": "linear-gradient(to bottom, #0284c7, #38bdf8)", "icon": "http://localhost:3845/assets/b2a974973084a0f77207218b1bb70518d07969d6.svg"}]}]}]}]}]}]}]}]}, "styles": {"colors": {"windowBackground": "#262626", "panelBackground": "#171717", "blockBackground": "#0a0a0a", "threadBackground": "#0a0a0a", "buttonBackground": "#0a0a0a", "primaryButtonBackground": "#082f49", "activeButtonGradient": {"from": "#0284c7", "to": "#38bdf8"}, "textColor": "#fafafa", "inputTextColor": "#fafafa", "subtleTextColor": "#525252", "caretColor": "#fafafa", "borderColor": "#404040", "focusBorderColor": "#0ea5e9", "separatorColor": "#404040"}, "dimensions": {"windowRadius": "10px", "controlsHeight": "40px", "controlButtonsWidth": "80px", "searchFieldWidth": "320px", "buttonHeight": "24px", "threadRadius": "16px", "containerRadius": "18px", "iconSize": "16px", "caretWidth": "1px", "caretHeight": "16px"}, "spacing": {"windowPadding": "0px", "controlsPadding": "8px", "threadPadding": "16px", "buttonPadding": "8px", "elementGap": "8px", "buttonGap": "8px", "componentGap": "4px"}, "typography": {"labelFont": {"family": "Inter", "style": "Medium", "size": "14px", "weight": 500, "lineHeight": "18px"}, "bodyFont": {"family": "Inter", "style": "Regular", "size": "16px", "weight": 400, "lineHeight": 1.75}, "searchFont": {"family": "Inter", "style": "Regular", "size": "12px", "weight": 400, "lineHeight": 1.5}}}, "designTokens": {"Text/text-regular": "#fafafa", "UI/label": "Font(family: \"Inter\", style: Medium, size: 14, weight: 500, lineHeight: 18)", "Fill/fill-regular-subtle": "#a3a3a3", "Border/border-line": "#262626", "Text/text-regular-subtlest": "#525252", "UI/body": "Font(family: \"Inter\", style: Regular, size: 12, weight: 400, lineHeight: 1.5)", "Background/bg-button": "#0a0a0a", "Fill/fill-inverse-subtle": "#ffffff8f", "Text/text-inverse": "#ffffff", "Background/bg-primary": "#082f49", "Background/bg-window": "#262626", "Border/border-divider": "#404040", "MD/body": "Font(family: \"Inter\", style: Regular, size: 16, weight: 400, lineHeight: 1.75)", "Text/text-caret": "#fafafa", "Fill/fill-regular": "#fafafa", "Border/border-edge": "#404040", "Fill/fill-inverse": "#ffffff", "Background/bg-block": "#0a0a0a", "Border/border-edge-focus": "#0ea5e9", "Background/bg-background": "#171717"}, "assets": {"images": [{"name": "img", "url": "http://localhost:3845/assets/f755507add3e05bbc8733477e2509b6458423722.svg", "type": "svg", "usage": "Dropdown arrow icon", "nodeId": "node-I2197_1799-2073_1057"}, {"name": "imgControlButtons", "url": "http://localhost:3845/assets/a8c1933f48906c52d8dcdebde6940ce54b9b153f.svg", "type": "svg", "usage": "Window control buttons (close, minimize, maximize)", "nodeId": "node-2197_2914"}, {"name": "imgVector12", "url": "http://localhost:3845/assets/a066c61403e8d3c3b89c3fe9ed2713330604d847.svg", "type": "svg", "usage": "Vertical separator line", "nodeId": "node-2197_2920"}, {"name": "img1", "url": "http://localhost:3845/assets/8fbd0a653349f9d584b284e27b4b4dacc349b655.svg", "type": "svg", "usage": "Search icon in text field", "nodeId": "node-I2197_2921-2078_500-67_131"}, {"name": "img2", "url": "http://localhost:3845/assets/77a2e8525b950f47935a890a3d0b0c634a6c8802.svg", "type": "svg", "usage": "Preview button icon", "nodeId": "node-I2255_6090-2179_873-2073_1094"}, {"name": "img3", "url": "http://localhost:3845/assets/63d2758c3528c6d24837045b6eed2943bf66e4c1.svg", "type": "svg", "usage": "Action button icon 1", "nodeId": "node-I2197_2925-2197_2696-2197_2150-2710_3989"}, {"name": "img4", "url": "http://localhost:3845/assets/bfbdd0e959226cb3822272b9f0a89dac5cafa174.svg", "type": "svg", "usage": "Action button icon 2", "nodeId": "node-I2197_2925-2197_2697-2197_2150-2073_1155"}, {"name": "img5", "url": "http://localhost:3845/assets/b2a974973084a0f77207218b1bb70518d07969d6.svg", "type": "svg", "usage": "Submit button icon (active state)", "nodeId": "node-I2197_2925-2197_2699-2197_2475-2073_1168"}]}, "components": {"Dropdown": {"interface": "DropdownProps", "props": {"iconType": {"type": "React.ReactNode | null", "default": null, "optional": true}, "labelText": {"type": "string", "default": "Label", "optional": true}, "state": {"type": "\"Default\" | \"Hovered\" | \"Focused\" | \"Disabled\"", "default": "<PERSON><PERSON><PERSON>", "optional": true}, "showIcon": {"type": "\"On\" | \"Off\"", "default": "On", "optional": true}}, "variants": [{"name": "Default with no icon", "state": "<PERSON><PERSON><PERSON>", "showIcon": "Off", "nodeId": "node-2197_1796"}]}, "Window": {"nodeId": "node-2197_2912", "features": ["Window controls", "Title bar with dropdown", "Search field", "Action buttons", "Input area with user text", "Active submit button"]}}, "interactions": {"windowControls": {"actions": ["close", "minimize", "maximize"], "functionality": "Standard window management"}, "dropdown": {"labelText": "Untitled", "showIcon": false, "states": ["<PERSON><PERSON><PERSON>", "Hovered", "Focused", "Disabled"]}, "searchField": {"placeholder": "Search", "icon": "search", "functionality": "Content search"}, "actionButtons": {"vsCodeButton": {"text": "Open with VS Code", "action": "open_vscode"}, "previewButton": {"text": "Preview", "icon": "preview", "primary": true, "action": "preview_content"}}, "inputArea": {"state": "with_content", "userInput": "Todo App with SQLite & Passkey", "focusBorder": "#0ea5e9", "caretVisible": true, "features": ["User text input visible", "Active text cursor", "Action buttons available", "Submit button active (gradient)"]}}, "states": {"inputWithContent": {"threadBorder": "#0ea5e9", "caretVisible": true, "textContent": "Todo App with SQLite & Passkey", "textColor": "#fafafa", "submitButtonActive": true, "submitButtonBackground": "linear-gradient(to bottom, #0284c7, #38bdf8)"}, "submitButton": {"inactive": {"background": "#262626", "iconColor": "#a3a3a3"}, "active": {"background": "linear-gradient(to bottom, #0284c7, #38bdf8)", "iconColor": "#ffffff"}}}, "userInput": {"content": "Todo App with SQLite & Passkey", "textColor": "#fafafa", "position": "active", "readyToSubmit": true}, "layout": {"windowLayout": {"type": "flex-column", "shadow": "0px 25px 50px 0px rgba(0,0,0,0.5), 0px 0px 0px 1px #404040", "borderRadius": "10px"}, "controlsLayout": {"type": "flex-row", "height": "40px", "sections": ["controls", "titlebar", "actions"]}, "contentLayout": {"type": "flex-row", "grow": true, "sections": ["leftPanel"]}, "inputLayout": {"type": "flex-column", "width": "800px", "centerAligned": true, "focusState": "active_with_content"}}, "technical": {"framework": "React", "language": "TypeScript", "styling": "Tailwind CSS", "alternativeCSS": "Vanilla CSS conversion needed if Tailwind not available", "dependencies": "None (avoid adding new dependencies)", "accessibility": {"considerations": ["keyboard navigation", "screen reader support", "color contrast", "focus management", "button accessibility", "input state announcements"], "requirements": ["proper alt texts for icons", "semantic HTML structure", "ARIA labels for buttons", "focus indicators", "input validation feedback"]}}, "metadata": {"extractedFrom": "Figma Dev Mode", "extractionDate": "2025-06-25", "pageName": "Prompt Entered (Ready to Submit)", "keyDifferences": ["User has entered text: 'Todo App with SQLite & Passkey'", "Text color changed from placeholder gray to white", "Submit button activated with blue gradient", "Text cursor remains visible", "Ready to submit state"], "nodeIds": ["node-2197_2912", "node-2197_2913", "node-2197_2914", "node-2197_2918", "node-2197_2919", "node-2197_2920", "node-2197_2921", "node-2255_6088", "node-2255_6089", "node-2255_6090", "node-2197_2922", "node-2197_2923", "node-2197_2924", "node-2197_5497", "node-2197_2925", "node-I2197_2925-2197_2692", "node-I2197_2925-2197_2693", "node-I2197_2925-2197_2694", "node-I2197_2925-2197_2695", "node-I2197_2925-2197_2696", "node-I2197_2925-2197_2697", "node-I2197_2925-2197_2698", "node-I2197_2925-2197_2699"], "codeConnectAvailable": false, "reason": "Code Connect is only available on Organization and Enterprise plans"}}