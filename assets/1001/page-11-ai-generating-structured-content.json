{"component": {"name": "Window", "id": "node-2224_5853", "type": "Application Window", "description": "AI生成过程中的页面，展示结构化内容，包含大标题、小标题、列表等内容类型，底部有折叠的代码块子对话"}, "structure": {"hierarchy": [{"name": "Window", "id": "node-2224_5853", "type": "div", "className": "bg-neutral-800 box-border content-stretch flex flex-col gap-px items-start justify-start overflow-clip p-0 relative rounded-[10px] shadow-[0px_25px_50px_0px_rgba(0,0,0,0.5),0px_0px_0px_1px_#404040] size-full", "children": [{"name": "WindowControls", "id": "node-2224_5854", "type": "div", "className": "bg-neutral-800 h-10 relative rounded-tl-[10px] rounded-tr-[10px] shrink-0 w-full", "children": [{"name": "ControlButtons", "id": "node-2224_5855", "type": "img", "className": "h-10 relative shrink-0 w-20", "src": "http://localhost:3845/assets/a8c1933f48906c52d8dcdebde6940ce54b9b153f.svg"}, {"name": "TopBar", "id": "node-2224_5859", "type": "div", "className": "basis-0 grow h-full min-h-px min-w-px relative shrink-0", "children": [{"name": "Dropdown", "id": "node-2224_5860", "type": "Dropdown", "className": "h-6 relative rounded-lg shrink-0", "props": {"labelText": "Untitled", "showIcon": "Off"}}, {"name": "Separator", "id": "node-2224_5861", "type": "img", "className": "h-3 relative shrink-0 w-0", "src": "http://localhost:3845/assets/a066c61403e8d3c3b89c3fe9ed2713330604d847.svg"}, {"name": "TextField", "id": "node-2224_5862", "type": "div", "className": "h-6 relative rounded-lg shrink-0 w-80", "children": [{"name": "SearchIcon", "id": "node-I2224_5862-2078_500", "type": "img", "src": "http://localhost:3845/assets/8fbd0a653349f9d584b284e27b4b4dacc349b655.svg"}, {"name": "SearchText", "id": "node-I2224_5862-2078_501", "type": "p", "text": "Search", "className": "font-['Inter:Regular',_sans-serif] font-normal leading-[0] not-italic relative shrink-0 text-[12px] text-left text-neutral-600 text-nowrap"}]}]}, {"name": "ActionButtons", "id": "node-2255_6144", "type": "div", "children": [{"name": "VSCodeButton", "id": "node-2255_6145", "type": "<PERSON><PERSON>", "className": "bg-neutral-950 h-6 relative rounded-lg shrink-0", "props": {"text": "Open with VS Code", "showShortcut": false, "icon1": "Off", "style": "Regular"}}, {"name": "PreviewButton", "id": "node-2255_6146", "type": "div", "className": "bg-sky-950 h-6 relative rounded-lg shrink-0", "text": "Preview", "icon": "http://localhost:3845/assets/77a2e8525b950f47935a890a3d0b0c634a6c8802.svg"}]}]}, {"name": "MainContent", "id": "node-2224_5863", "type": "div", "className": "box-border content-stretch flex flex-row gap-px h-[869px] items-start justify-center overflow-clip p-0 relative shrink-0 w-full", "children": [{"name": "LeftPanel", "id": "node-2224_5864", "type": "div", "className": "basis-0 bg-neutral-900 grow h-full min-h-px min-w-px relative shrink-0", "children": [{"name": "Catalogue", "id": "node-2224_5873", "type": "div", "className": "absolute left-0 top-0 w-[296px]", "position": "sidebar", "children": [{"name": "CatalogueIcon", "id": "node-2224_5874", "type": "button", "className": "relative rounded-lg shrink-0", "borderColor": "#404040", "icon": "http://localhost:3845/assets/aa475550f155f01867b51ee48023a602e10ae4ca.svg"}, {"name": "MainCatalogueItem", "id": "node-2224_7764", "type": "div", "className": "box-border content-stretch flex flex-row gap-1 h-[21px] items-center justify-start p-0 relative rounded-lg shrink-0 w-full", "children": [{"name": "LoadingIcon", "id": "node-2224_7765", "type": "div", "className": "overflow-clip relative shrink-0 size-4", "rotation": "90deg", "animation": "loading_spin", "icon": "http://localhost:3845/assets/5f89302f7334c2b89bb5c808f2fd9b33f2ef76d6.svg"}, {"name": "MainCatalogueText", "id": "node-2224_7766", "type": "p", "text": "Build a Todo App with SQLite and Passkey Authentication", "className": "basis-0 font-['Inter:Medium',_sans-serif] font-medium grow leading-[0] min-h-px min-w-px not-italic overflow-ellipsis overflow-hidden relative shrink-0 text-[14px] text-left text-neutral-600 text-nowrap"}]}, {"name": "CurrentPageTitle", "id": "node-2224_7736", "type": "p", "text": "Todo App with SQLite & Passkey", "className": "font-['Inter:Medium',_sans-serif] font-medium leading-[0] min-w-full not-italic overflow-ellipsis overflow-hidden relative shrink-0 text-[14px] text-left text-nowrap text-sky-500", "textColor": "#0ea5e9", "state": "active"}, {"name": "SubSection1", "id": "node-2224_5875", "type": "div", "className": "h-[21px] relative rounded-lg shrink-0 w-full", "children": [{"name": "SubSectionText1", "id": "node-2224_5877", "type": "p", "text": "🔧 Tech Stack", "className": "basis-0 font-['Inter:Medium',_sans-serif] font-medium grow leading-[0] min-h-px min-w-px not-italic overflow-ellipsis overflow-hidden relative shrink-0 text-[14px] text-left text-neutral-50 text-nowrap", "indentation": "level-1"}]}, {"name": "SubSection2", "id": "node-2224_7769", "type": "div", "className": "h-[21px] relative rounded-lg shrink-0 w-full", "children": [{"name": "SubSectionText2", "id": "node-2224_7770", "type": "p", "text": "🗂 Project Structure", "className": "basis-0 font-['Inter:Medium',_sans-serif] font-medium grow leading-[0] min-h-px min-w-px not-italic overflow-ellipsis overflow-hidden relative shrink-0 text-[14px] text-left text-neutral-50 text-nowrap", "indentation": "level-1"}]}]}, {"name": "ContentArea", "id": "node-2255_6184", "type": "div", "className": "relative shrink-0 w-[800px]", "children": [{"name": "PromptBlock", "id": "node-2255_6185", "type": "div", "className": "basis-0 grow min-h-px min-w-px relative rounded-[18px] shrink-0", "children": [{"name": "PromptPanel", "id": "node-2255_6186", "type": "div", "className": "bg-[rgba(255,255,255,0.03)] relative rounded-2xl shrink-0 w-full", "background": "rgba(255,255,255,0.03)", "state": "collapsed", "children": [{"name": "PromptContainer", "id": "node-2255_6187", "type": "div", "className": "box-border content-stretch flex flex-row gap-1 items-start justify-start p-0 relative shrink-0 w-full", "children": [{"name": "Prompt<PERSON><PERSON><PERSON>", "id": "node-2255_6188", "type": "p", "text": "Prompt:", "className": "basis-0 font-['Inter:Medium',_sans-serif] font-medium grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[14px] text-left text-neutral-600"}, {"name": "PromptInfo", "id": "node-2255_6189", "type": "div", "className": "box-border content-stretch flex flex-row gap-1 items-center justify-start p-0 relative shrink-0", "children": [{"name": "ChevronIcon", "id": "node-2255_6191", "type": "img", "src": "http://localhost:3845/assets/d0323811a35893ff3c0d134c2d3911a22b855745.svg", "className": "overflow-clip relative shrink-0 size-4", "state": "collapsed"}]}]}, {"name": "PromptText", "id": "node-2255_6192", "type": "p", "text": "Todo App with SQLite & Passkey", "className": "font-['Inter:Regular',_sans-serif] font-normal leading-[0] not-italic relative shrink-0 text-[16px] text-left text-neutral-400 w-full"}]}]}, {"name": "MainTitleBlock", "id": "node-2224_5904", "type": "div", "className": "relative shrink-0 w-[800px]", "contentType": "main_title", "children": [{"name": "MainTitleContainer", "id": "node-I2224_5904-2139_218", "type": "div", "className": "basis-0 grow min-h-px min-w-px relative rounded shrink-0", "children": [{"name": "MainTitle", "id": "node-I2224_5904-2139_217", "type": "h1", "text": "Todo App with SQLite & Passkey", "className": "font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic relative shrink-0 text-[32px] text-left text-neutral-50 text-nowrap", "typography": "MD/h1", "semanticLevel": "h1"}]}]}, {"name": "TechStackSectionBlock", "id": "node-2224_5905", "type": "div", "className": "relative shrink-0 w-[800px]", "contentType": "section_title", "children": [{"name": "TechStackContainer", "id": "node-I2224_5905-2149_508", "type": "div", "className": "basis-0 grow min-h-px min-w-px relative rounded shrink-0", "children": [{"name": "TechStackTitle", "id": "node-I2224_5905-2149_509", "type": "h2", "text": "🔧 Tech Stack", "className": "font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic relative shrink-0 text-[28px] text-left text-neutral-50 text-nowrap", "typography": "MD/h2", "semanticLevel": "h2", "emoji": "🔧"}]}]}, {"name": "TechStackListBlock", "id": "node-2224_5913", "type": "div", "className": "relative shrink-0 w-[800px]", "contentType": "bulleted_list", "children": [{"name": "TechStackListContainer", "id": "node-I2224_5913-2149_778", "type": "div", "className": "basis-0 grow min-h-px min-w-px relative rounded shrink-0", "children": [{"name": "TechStackList", "id": "node-I2224_5913-2149_779", "type": "ul", "className": "basis-0 font-['Inter:Regular',_sans-serif] font-normal grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[16px] text-left text-neutral-50", "listType": "bulleted", "items": [{"text": "Frontend: Vue 3 + Vite + Pinia + Tailwind CSS (optional)", "type": "li"}, {"text": "Backend: Node.js + Express", "type": "li"}, {"text": "Database: SQLite (via better-sqlite3 or sqlite3)", "type": "li"}, {"text": "Auth: Passkey (WebAuthn + FIDO2, using @simplewebauthn/server)", "type": "li"}]}]}]}, {"name": "ProjectStructureSectionBlock", "id": "node-2224_5983", "type": "div", "className": "relative shrink-0 w-[800px]", "contentType": "section_title", "children": [{"name": "ProjectStructureContainer", "id": "node-I2224_5983-2149_508", "type": "div", "className": "basis-0 grow min-h-px min-w-px relative rounded shrink-0", "children": [{"name": "ProjectStructureTitle", "id": "node-I2224_5983-2149_509", "type": "h2", "text": "🗂 Project Structure", "className": "font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic relative shrink-0 text-[28px] text-left text-neutral-50 text-nowrap", "typography": "MD/h2", "semanticLevel": "h2", "emoji": "🗂"}]}]}, {"name": "CollapsedCodeBlock", "id": "node-2224_5865", "type": "div", "className": "relative shrink-0 w-[800px]", "contentType": "collapsed_sub_conversation", "children": [{"name": "CodeBlockContainer", "id": "node-2224_5866", "type": "div", "className": "basis-0 grow min-h-px min-w-px relative rounded-[18px] shrink-0", "children": [{"name": "CodeBlockPanel", "id": "node-2224_5867", "type": "div", "className": "bg-neutral-950 relative rounded-2xl shrink-0 w-full", "state": "collapsed", "subConversationType": "nested_dialogue", "children": [{"name": "CodeHeader", "id": "node-2224_5868", "type": "div", "className": "h-12 relative shrink-0 w-full", "children": [{"name": "FileInfo", "id": "node-2224_5869", "type": "div", "className": "basis-0 box-border content-stretch flex flex-row gap-1 grow items-center justify-start min-h-px min-w-px p-0 relative shrink-0", "children": [{"name": "LoadingIcon", "id": "node-2224_5870", "type": "div", "className": "overflow-clip relative shrink-0 size-6", "children": [{"name": "LoadingAnimation", "id": "node-2224_5871", "type": "div", "className": "absolute left-1/2 overflow-clip size-4 top-1/2 translate-x-[-50%] translate-y-[-50%]", "rotation": "90deg", "animation": "loading_spin", "icon": "http://localhost:3845/assets/5f89302f7334c2b89bb5c808f2fd9b33f2ef76d6.svg"}]}, {"name": "CodeTitle", "id": "node-2224_5872", "type": "p", "text": "Build a Todo App with SQLite and Passkey Authentication", "className": "bg-clip-text bg-gradient-to-r bg-neutral-50 font-['Inter:Medium',_sans-serif] font-medium from-[#fafafa] leading-[0] not-italic relative shrink-0 text-[16px] text-left text-neutral-50 text-nowrap to-[#404040]", "gradient": "linear-gradient(to right, #fafafa, #404040)", "style": "WebkitTextFillColor: transparent"}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "id": "node-2224_5988", "type": "img", "src": "http://localhost:3845/assets/ab942ab48a7bf6e0ed1ecd3021074e9958dd8add.svg", "className": "overflow-clip relative shrink-0 size-4", "state": "collapsed"}]}]}]}]}]}]}]}]}]}, "styles": {"colors": {"windowBackground": "#262626", "panelBackground": "#171717", "blockBackground": "#0a0a0a", "promptPanelBackground": "rgba(255,255,255,0.03)", "codeBlockBackground": "#0a0a0a", "buttonBackground": "#0a0a0a", "primaryButtonBackground": "#082f49", "textColor": "#fafafa", "promptTextColor": "#a3a3a3", "labelTextColor": "#525252", "generatedTextColor": "#fafafa", "catalogueTextColor": "#525252", "activeCatalogueTextColor": "#0ea5e9", "h1TextColor": "#fafafa", "h2TextColor": "#fafafa", "listTextColor": "#fafafa", "borderColor": "#404040", "codeHeaderBorder": "#262626", "separatorColor": "#404040"}, "dimensions": {"windowRadius": "10px", "controlsHeight": "40px", "controlButtonsWidth": "80px", "searchFieldWidth": "320px", "buttonHeight": "24px", "panelRadius": "16px", "containerRadius": "18px", "codeHeaderHeight": "48px", "iconSize": "16px", "loadingIconSize": "12px", "catalogueWidth": "296px", "contentWidth": "800px"}, "spacing": {"windowPadding": "0px", "controlsPadding": "8px", "panelPadding": "16px", "codeContentPadding": "24px 16px", "buttonPadding": "8px", "elementGap": "24px", "buttonGap": "8px", "componentGap": "4px", "cataloguePadding": "32px 0px 32px 40px", "listIndentation": "20px", "sidebarIndentation": "20px"}, "effects": {"loadingRotation": "90deg", "gradientText": "linear-gradient(to right, #fafafa, #404040)", "windowShadow": "0px 25px 50px 0px rgba(0,0,0,0.5), 0px 0px 0px 1px #404040", "promptPanelOpacity": "0.03", "collapsedState": "content_hidden"}, "typography": {"labelFont": {"family": "Inter", "style": "Medium", "size": "14px", "weight": 500, "lineHeight": "18px"}, "bodyFont": {"family": "Inter", "style": "Regular", "size": "16px", "weight": 400, "lineHeight": 1.75}, "h1Font": {"family": "Inter", "style": "Semi Bold", "size": "32px", "weight": 600, "lineHeight": 1.2}, "h2Font": {"family": "Inter", "style": "Semi Bold", "size": "28px", "weight": 600, "lineHeight": 1.2}, "codeTitleFont": {"family": "Inter", "style": "Medium", "size": "16px", "weight": 500, "lineHeight": "20px"}, "searchFont": {"family": "Inter", "style": "Regular", "size": "12px", "weight": 400, "lineHeight": 1.5}}}, "designTokens": {"Text/text-regular": "#fafafa", "UI/label": "Font(family: \"Inter\", style: Medium, size: 14, weight: 500, lineHeight: 18)", "Fill/fill-regular-subtle": "#a3a3a3", "Border/border-line": "#262626", "Text/text-regular-subtlest": "#525252", "UI/body": "Font(family: \"Inter\", style: Regular, size: 12, weight: 400, lineHeight: 1.5)", "Background/bg-button": "#0a0a0a", "Fill/fill-inverse-subtle": "#ffffff8f", "Text/text-inverse": "#ffffff", "Background/bg-primary": "#082f49", "Background/bg-window": "#262626", "Border/border-divider": "#404040", "Fill/fill-regular-subtlest": "#525252", "Text/text-regular-subtle": "#a3a3a3", "MD/body": "Font(family: \"Inter\", style: Regular, size: 16, weight: 400, lineHeight: 1.75)", "Background/bg-surface": "#ffffff08", "MD/h1": "Font(family: \"Inter\", style: Semi Bold, size: 32, weight: 600, lineHeight: 1.2000000476837158)", "MD/h2": "Font(family: \"Inter\", style: Semi Bold, size: 28, weight: 600, lineHeight: 1.2000000476837158)", "Fill/fill-regular": "#fafafa", "Text/text-regular-disable": "#404040", "MD/label": "Font(family: \"Inter\", style: Medium, size: 16, weight: 500, lineHeight: 20)", "Background/bg-block": "#0a0a0a", "Border/border-edge": "#404040", "Text/text-brand": "#0ea5e9", "Background/bg-background": "#171717"}, "assets": {"images": [{"name": "img", "url": "http://localhost:3845/assets/f755507add3e05bbc8733477e2509b6458423722.svg", "type": "svg", "usage": "Dropdown arrow icon", "nodeId": "node-I2197_1799-2073_1057"}, {"name": "imgControlButtons", "url": "http://localhost:3845/assets/a8c1933f48906c52d8dcdebde6940ce54b9b153f.svg", "type": "svg", "usage": "Window control buttons (close, minimize, maximize)", "nodeId": "node-2224_5855"}, {"name": "imgVector12", "url": "http://localhost:3845/assets/a066c61403e8d3c3b89c3fe9ed2713330604d847.svg", "type": "svg", "usage": "Vertical separator line", "nodeId": "node-2224_5861"}, {"name": "img1", "url": "http://localhost:3845/assets/8fbd0a653349f9d584b284e27b4b4dacc349b655.svg", "type": "svg", "usage": "Search icon in text field", "nodeId": "node-I2224_5862-2078_500-67_131"}, {"name": "img2", "url": "http://localhost:3845/assets/77a2e8525b950f47935a890a3d0b0c634a6c8802.svg", "type": "svg", "usage": "Preview button icon", "nodeId": "node-I2255_6146-2179_873-2073_1094"}, {"name": "img3", "url": "http://localhost:3845/assets/d0323811a35893ff3c0d134c2d3911a22b855745.svg", "type": "svg", "usage": "Chevron icon in prompt panel (collapsed state)", "nodeId": "node-I2255_6191-2073_1059"}, {"name": "img4", "url": "http://localhost:3845/assets/5f89302f7334c2b89bb5c808f2fd9b33f2ef76d6.svg", "type": "svg", "usage": "Loading spinner icon (multiple instances)", "nodeId": "node-I2224_5871-2202_6786-2073_1133"}, {"name": "img5", "url": "http://localhost:3845/assets/ab942ab48a7bf6e0ed1ecd3021074e9958dd8add.svg", "type": "svg", "usage": "Chevron icon in code header (collapsed state)", "nodeId": "node-I2224_5988-2202_6419"}, {"name": "img6", "url": "http://localhost:3845/assets/aa475550f155f01867b51ee48023a602e10ae4ca.svg", "type": "svg", "usage": "Catalogue icon in sidebar", "nodeId": "node-I2224_5874-2197_2150-2073_1127"}]}, "components": {"Dropdown": {"interface": "DropdownProps", "props": {"iconType": {"type": "React.ReactNode | null", "default": null, "optional": true}, "labelText": {"type": "string", "default": "Label", "optional": true}, "state": {"type": "\"Default\" | \"Hovered\" | \"Focused\" | \"Disabled\"", "default": "<PERSON><PERSON><PERSON>", "optional": true}, "showIcon": {"type": "\"On\" | \"Off\"", "default": "On", "optional": true}}}, "Button": {"interface": "ButtonProps", "props": {"text": {"type": "string", "default": "Label", "optional": true}, "icon": {"type": "React.ReactNode | null", "default": null, "optional": true}, "showShortcut": {"type": "boolean", "default": true, "optional": true}, "icon1": {"type": "\"Off\" | \"On\"", "default": "On", "optional": true}, "style": {"type": "\"Suggest\" | \"Disabled\" | \"Regular\"", "default": "Suggest", "optional": true}, "state": {"type": "\"Default\" | \"Hovered\" | \"Focused\" | \"Loading\" | \"Pressed\"", "default": "<PERSON><PERSON><PERSON>", "optional": true}, "size": {"type": "\"Small\" | \"Medium\"", "default": "Small", "optional": true}}}, "Window": {"nodeId": "node-2224_5853", "features": ["Window controls", "Title bar with dropdown", "Search field", "Action buttons", "Collapsed prompt panel", "Structured content blocks", "Hierarchical sidebar navigation", "Collapsed sub-conversation block"]}, "PromptPanel": {"nodeId": "node-2255_6186", "type": "Prompt Display", "background": "rgba(255,255,255,0.03)", "state": "collapsed", "features": ["Prompt label", "Collapsed chevron indicator", "Submitted prompt text"]}, "StructuredContent": {"type": "Content Blocks", "contentTypes": ["main_title (h1)", "section_title (h2)", "bulleted_list", "collapsed_sub_conversation"], "features": ["Semantic HTML structure", "Typography hierarchy", "Emoji icons in titles", "Expandable sub-conversations"]}, "HierarchicalSidebar": {"nodeId": "node-2224_5873", "type": "Navigation Sidebar", "features": ["Main catalogue item with loading", "Active page indicator (blue text)", "Sub-section navigation", "Indented hierarchy", "Real-time content reflection"]}, "LoadingAnimation": {"nodeId": "node-2224_5871", "type": "Loading Spinner", "rotation": "90deg", "animationType": "continuous_rotation", "size": "16px", "locations": ["code_header", "sidebar_catalogue"]}}, "interactions": {"windowControls": {"actions": ["close", "minimize", "maximize"], "functionality": "Standard window management"}, "dropdown": {"labelText": "Untitled", "showIcon": false, "states": ["<PERSON><PERSON><PERSON>", "Hovered", "Focused", "Disabled"]}, "searchField": {"placeholder": "Search", "icon": "search", "functionality": "Content search"}, "actionButtons": {"vsCodeButton": {"text": "Open with VS Code", "action": "open_vscode", "component": "<PERSON><PERSON>", "showShortcut": false}, "previewButton": {"text": "Preview", "icon": "preview", "primary": true, "action": "preview_content"}}, "promptPanel": {"state": "collapsed", "promptText": "Todo App with SQLite & Passkey", "collapsible": true, "expandable": true, "clickToExpand": true}, "structuredContent": {"mainTitle": {"text": "Todo App with SQLite & Passkey", "level": "h1", "semantics": "main_heading"}, "techStackSection": {"title": "🔧 Tech Stack", "level": "h2", "emoji": "🔧", "content": "bulleted_list"}, "projectStructureSection": {"title": "🗂 Project Structure", "level": "h2", "emoji": "🗂", "content": "pending_generation"}}, "collapsedSubConversation": {"state": "collapsed", "title": "Build a Todo App with SQLite and Passkey Authentication", "titleGradient": true, "loadingAnimation": true, "expandable": true, "clickToExpand": true, "hiddenContent": ["detailed conversation history", "code examples", "sub-dialogues", "nested interactions"]}, "hierarchicalSidebar": {"mainItem": {"text": "Build a Todo App with SQLite and Passkey Authentication", "state": "active_with_loading", "loadingAnimation": true}, "activePageIndicator": {"text": "Todo App with SQLite & Passkey", "color": "#0ea5e9", "state": "current_page"}, "subSections": [{"text": "🔧 Tech Stack", "indentation": "level-1", "state": "generated"}, {"text": "🗂 Project Structure", "indentation": "level-1", "state": "generating"}]}}, "states": {"aiGenerating": {"promptPanel": {"state": "collapsed", "background": "rgba(255,255,255,0.03)", "promptTextVisible": true, "chevronDirection": "collapsed"}, "structuredContent": {"mainTitleGenerated": true, "techStackSectionGenerated": true, "techStackListGenerated": true, "projectStructureTitleGenerated": true, "projectStructureContentPending": true}, "subConversation": {"state": "collapsed", "headerVisible": true, "contentHidden": true, "loadingAnimation": true, "chevronDirection": "collapsed"}, "sidebar": {"catalogueVisible": true, "loadingAnimation": true, "activePageIndicator": "#0ea5e9", "hierarchyReflected": true, "subSectionsVisible": true}}}, "animations": {"loadingSpinner": {"type": "rotation", "duration": "continuous", "direction": "clockwise", "initialRotation": "90deg", "elements": ["code_header_icon", "sidebar_loading_icon"]}, "gradientTitle": {"type": "text_gradient", "colors": ["#fafafa", "#404040"], "direction": "left_to_right"}}, "contentStructure": {"generatedContent": [{"type": "main_title", "content": "Todo App with SQLite & Passkey", "typography": "MD/h1", "semanticLevel": "h1"}, {"type": "section_title", "content": "🔧 Tech Stack", "typography": "MD/h2", "semanticLevel": "h2", "emoji": "🔧"}, {"type": "bulleted_list", "items": ["Frontend: Vue 3 + Vite + Pinia + Tailwind CSS (optional)", "Backend: Node.js + Express", "Database: SQLite (via better-sqlite3 or sqlite3)", "Auth: Passkey (WebAuthn + FIDO2, using @simplewebauthn/server)"], "listStyle": "disc"}, {"type": "section_title", "content": "🗂 Project Structure", "typography": "MD/h2", "semanticLevel": "h2", "emoji": "🗂"}], "collapsedSubConversation": {"description": "Nested dialogue block collapsed at bottom", "title": "Build a Todo App with SQLite and Passkey Authentication", "state": "collapsed_with_loading", "expandable": true, "hiddenConversation": ["Detailed implementation steps", "Code examples and snippets", "Sub-questions and clarifications", "Technical specifications"]}}, "layout": {"windowLayout": {"type": "flex-column", "shadow": "0px 25px 50px 0px rgba(0,0,0,0.5), 0px 0px 0px 1px #404040", "borderRadius": "10px"}, "controlsLayout": {"type": "flex-row", "height": "40px", "sections": ["controls", "titlebar", "actions"]}, "contentLayout": {"type": "flex-row", "height": "869px", "sections": ["leftPanel_with_sidebar"]}, "structuredContentLayout": {"type": "flex-column", "width": "800px", "centerAligned": true, "spacing": "24px", "sections": ["collapsed_prompt", "main_title", "section_title_1", "bulleted_list", "section_title_2", "collapsed_sub_conversation"]}, "sidebarLayout": {"type": "absolute", "position": "left_overlay", "width": "296px", "padding": "32px 0px 32px 40px", "hierarchy": "nested_with_indentation"}}, "technical": {"framework": "React", "language": "TypeScript", "styling": "Tailwind CSS", "alternativeCSS": "Vanilla CSS conversion needed if Tailwind not available", "dependencies": "None (avoid adding new dependencies)", "accessibility": {"considerations": ["semantic HTML structure with proper headings", "keyboard navigation for collapsible elements", "screen reader announcements for dynamic content", "hierarchical sidebar navigation", "loading state indicators", "expandable content clearly indicated"], "requirements": ["proper heading hierarchy (h1, h2)", "ARIA labels for collapsible regions", "semantic list markup", "loading announcements", "expanded/collapsed state indicators", "keyboard accessibility for all interactions"]}, "animations": {"loadingSpinner": {"implementation": "CSS animation or React transition", "duration": "continuous", "easing": "linear"}, "gradientTitle": {"implementation": "CSS gradient with webkit text fill", "effect": "transparent text with gradient background"}}}, "metadata": {"extractedFrom": "Figma Dev Mode", "extractionDate": "2025-06-25", "pageName": "AI Generating Structured Content", "sequenceNumber": 11, "previousPage": "Page 10 - Conversation Collapsed", "keyFeatures": ["Structured content generation with semantic hierarchy", "Multiple content types (titles, lists, sub-conversations)", "Hierarchical sidebar navigation reflecting content structure", "Collapsed sub-conversation block", "Active page indication in sidebar", "Emoji icons in section titles"], "keyDifferences": ["Added structured content blocks with semantic HTML", "Hierarchical sidebar with active page indicator (#0ea5e9)", "Multiple typography levels (h1: 32px, h2: 28px)", "Bulleted list with specific tech stack items", "Sub-conversation collapsed at bottom", "Emoji integration in section titles"], "contentTypes": ["Main title (h1): Todo App with SQLite & Passkey", "Section title (h2): 🔧 Tech Stack", "Bulleted list: Frontend, Backend, Database, Auth items", "Section title (h2): 🗂 Project Structure", "Collapsed sub-conversation: Detailed implementation"], "sidebarHierarchy": ["Main item: Build a Todo App with SQLite and Passkey Authentication (loading)", "Active page: Todo App with SQLite & Passkey (blue)", "Sub-section: 🔧 Tech Stack (indented)", "Sub-section: 🗂 Project Structure (indented)"], "nodeIds": ["node-2224_5853", "node-2224_5854", "node-2224_5855", "node-2224_5859", "node-2224_5860", "node-2224_5861", "node-2224_5862", "node-2255_6144", "node-2255_6145", "node-2255_6146", "node-2224_5863", "node-2224_5864", "node-2224_5873", "node-2224_5874", "node-2224_7764", "node-2224_7765", "node-2224_7766", "node-2224_7736", "node-2224_5875", "node-2224_5877", "node-2224_7769", "node-2224_7770", "node-2255_6184", "node-2255_6185", "node-2255_6186", "node-2255_6187", "node-2255_6188", "node-2255_6189", "node-2255_6191", "node-2255_6192", "node-2224_5904", "node-I2224_5904-2139_218", "node-I2224_5904-2139_217", "node-2224_5905", "node-I2224_5905-2149_508", "node-I2224_5905-2149_509", "node-2224_5913", "node-I2224_5913-2149_778", "node-I2224_5913-2149_779", "node-2224_5983", "node-I2224_5983-2149_508", "node-I2224_5983-2149_509", "node-2224_5865", "node-2224_5866", "node-2224_5867", "node-2224_5868", "node-2224_5869", "node-2224_5870", "node-2224_5871", "node-2224_5872", "node-2224_5988"], "codeConnectAvailable": false, "reason": "Code Connect is only available on Organization and Enterprise plans"}}