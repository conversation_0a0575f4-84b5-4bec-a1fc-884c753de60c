{"component": {"name": "LeftPanel", "id": "node-2139_189", "type": "Container", "description": "左侧面板组件，包含文本输入区域和菜单触发器，主要用于AI辅助写作功能"}, "structure": {"hierarchy": [{"name": "LeftPanel", "id": "node-2139_189", "type": "div", "className": "bg-neutral-900 relative size-full", "children": [{"name": "Block", "id": "node-2197_1667", "type": "div", "className": "relative shrink-0 w-[800px]", "children": [{"name": "TextContainer", "id": "node-2197_1668", "type": "div", "className": "basis-0 grow min-h-px min-w-px relative rounded shrink-0", "children": [{"name": "PlaceholderText", "id": "node-2197_1669", "type": "p", "text": "Write, press 'space' for AI", "className": "font-['Inter:Regular',_sans-serif] font-normal leading-[0] not-italic relative shrink-0 text-[16px] text-left text-neutral-600 text-nowrap"}, {"name": "<PERSON><PERSON>", "id": "node-2197_1670", "type": "div", "className": "absolute h-4 left-1 top-[9px] w-px"}]}, {"name": "<PERSON>uTrigger", "id": "node-2197_1671", "type": "div", "className": "absolute bg-neutral-950 h-6 left-2 rounded-lg top-[5px]", "children": [{"name": "TriggerIcon1", "id": "node-I2197_1671-2142_275", "type": "<PERSON><PERSON>", "props": {"icon": null, "type": "<PERSON><PERSON><PERSON>"}}, {"name": "SeparatorIcon", "id": "node-I2197_1671-2142_249", "type": "div", "className": "flex-none rotate-[90deg]"}, {"name": "TriggerIcon2", "id": "node-I2197_1671-2142_283", "type": "<PERSON><PERSON>", "props": {"icon": "Draggable", "type": "<PERSON><PERSON><PERSON>"}}]}]}]}]}, "styles": {"colors": {"background": "#171717", "containerBackground": "#0a0a0a", "textColor": "#525252", "caretColor": "#fafafa", "iconColor": "#fafafa", "borderColor": "#404040"}, "dimensions": {"containerWidth": "800px", "containerHeight": "24px", "iconSize": "16px", "caretWidth": "1px", "caretHeight": "16px", "borderWidth": "1px"}, "spacing": {"blockPadding": {"horizontal": "64px", "vertical": "48px"}, "menuTriggerPadding": "4px", "elementGap": "10px", "borderRadius": "8px"}, "typography": {"fontFamily": "Inter", "fontWeight": 400, "fontSize": "16px", "lineHeight": 1.75, "fontStyle": "Regular"}}, "designTokens": {"Text/text-regular-subtlest": "#525252", "MD/body": "Font(family: \"Inter\", style: Regular, size: 16, weight: 400, lineHeight: 1.75)", "Text/text-caret": "#fafafa", "Fill/fill-regular": "#fafafa", "Border/border-divider": "#404040", "Base/button": "#0a0a0a", "Base/edge": "#404040", "Background/bg-background": "#171717"}, "assets": {"images": [{"name": "img", "url": "http://localhost:3845/assets/7ca200174db898d1413376ff5c52683a0c7ee762.svg", "type": "svg", "usage": "Primary icon in trigger component", "nodeId": "node-I2142_259-2073_1121"}, {"name": "imgVector", "url": "http://localhost:3845/assets/9ed89d85bec2b3381202c836e8be1ae5355efd54.svg", "type": "svg", "usage": "Draggable vector icon", "nodeId": "node-2073_1110"}, {"name": "img1", "url": "http://localhost:3845/assets/42235bc65995b3bc46ec09f463c5afcb920abd05.svg", "type": "svg", "usage": "Separator icon (rotated 90deg)", "nodeId": "node-I2197_1671-2142_249"}]}, "components": {"Trigger": {"interface": "TriggerProps", "props": {"icon": {"type": "React.ReactNode | null", "default": null, "optional": true}, "type": {"type": "\"Default\" | \"Hovered\"", "default": "<PERSON><PERSON><PERSON>", "optional": true}}, "nodeId": "node-2142_266"}, "Draggable": {"nodeId": "node-2073_1109", "dimensions": {"width": "8px", "height": "10px"}, "position": {"left": "4px", "top": "3px"}}}, "interactions": {"textInput": {"placeholder": "Write, press 'space' for AI", "trigger": "space key", "functionality": "AI assistance activation"}, "menuTrigger": {"states": ["<PERSON><PERSON><PERSON>", "Hovered"], "actions": ["click", "drag"], "icons": ["primary", "separator", "draggable"]}}, "layout": {"flexDirection": "column", "alignment": "center", "distribution": "start", "positioning": {"type": "relative", "fullSize": true}, "responsiveness": {"fixedWidth": "800px", "centerAligned": true, "iconFlexible": true}}, "technical": {"framework": "React", "language": "TypeScript", "styling": "Tailwind CSS", "alternativeCSS": "Vanilla CSS conversion needed if Tailwind not available", "dependencies": "None (avoid adding new dependencies)", "accessibility": {"considerations": ["keyboard navigation", "screen reader support", "color contrast"], "requirements": ["proper alt texts", "semantic HTML", "focus management"]}}, "metadata": {"extractedFrom": "Figma Dev Mode", "extractionDate": "2025-06-25", "nodeIds": ["node-2139_189", "node-2197_1667", "node-2197_1668", "node-2197_1669", "node-2197_1670", "node-2197_1671", "node-I2197_1671-2142_275", "node-I2197_1671-2142_249", "node-I2197_1671-2142_283", "node-2142_266", "node-I2142_259-2073_1121", "node-2073_1109", "node-2073_1110"], "codeConnectAvailable": false, "reason": "Code Connect is only available on Organization and Enterprise plans"}}